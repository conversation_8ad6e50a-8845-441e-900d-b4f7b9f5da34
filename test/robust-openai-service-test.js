const fs = require('fs');

// Load environment variables
const envContent = fs.readFileSync('.env', 'utf8');
const envVars = {};
envContent.split('\n').forEach(line => {
  const [key, value] = line.split('=');
  if (key && value) {
    envVars[key.trim()] = value.trim();
  }
});
process.env.OPENAI_API_KEY = envVars.OPENAI_API_KEY;

// Import the robust OpenAI service
const { RobustOpenAIService, RobustOpenAIResponse } = require('../services/robust-openai-service.js');

// Test the robust OpenAI service
async function testRobustOpenAIService() {
  try {
    console.log('=== Testing Robust OpenAI Service ===\n');

    const service = new RobustOpenAIService({
      apiKey: process.env.OPENAI_API_KEY,
      defaultModel: 'gpt-4o-mini'
    });

    // Test 1: Basic chat functionality
    console.log('1. Testing basic chat functionality...');
    const basicResponse = await service.chat([
      { role: "system", content: "You are a helpful assistant." },
      { role: "user", content: "Say hello in exactly 5 words." }
    ]);
    
    console.log('Basic Response:', basicResponse.getContent());
    console.log('Usage:', basicResponse.getUsage());
    console.log('Finish Reason:', basicResponse.getFinishReason());
    console.log('');

    // Test 2: Tool calling functionality
    console.log('2. Testing tool calling...');
    
    const weatherTool = {
      type: "function",
      function: {
        name: "get_weather",
        description: "Get current weather for a location",
        parameters: {
          type: "object",
          properties: {
            location: {
              type: "string",
              description: "The city and state, e.g. San Francisco, CA"
            },
            unit: {
              type: "string",
              enum: ["celsius", "fahrenheit"],
              description: "Temperature unit"
            }
          },
          required: ["location"]
        }
      }
    };

    const toolResponse = await service.callWithTools(
      [{ role: "user", content: "What's the weather like in Boston?" }],
      [weatherTool],
      "auto"
    );

    if (toolResponse.hasToolCalls()) {
      const toolCall = toolResponse.getFirstToolCall();
      console.log('Tool called:', toolCall.function.name);
      console.log('Tool arguments:', JSON.parse(toolCall.function.arguments));
      console.log('Tool call ID:', toolCall.id);
    } else {
      console.log('No tool calls made. Response:', toolResponse.getContent());
    }
    console.log('');

    // Test 3: Tool workflow execution
    console.log('3. Testing complete tool workflow...');
    
    const mockToolExecutor = async (toolCall) => {
      if (toolCall.function.name === "get_weather") {
        const args = JSON.parse(toolCall.function.arguments);
        return {
          location: args.location,
          temperature: 22,
          unit: args.unit || "celsius",
          description: "Partly cloudy",
          humidity: 65
        };
      }
      throw new Error(`Unknown tool: ${toolCall.function.name}`);
    };

    const workflowResponse = await service.executeToolWorkflow(
      [{ role: "user", content: "What's the weather in Boston and give me a summary?" }],
      [weatherTool],
      mockToolExecutor
    );

    console.log('Final workflow response:', workflowResponse.getContent());
    console.log('');

    // Test 4: JSON object response with schema
    console.log('4. Testing JSON object response with schema...');
    
    const personSchema = {
      type: "object",
      properties: {
        name: { type: "string" },
        age: { type: "number" },
        occupation: { type: "string" },
        skills: { 
          type: "array",
          items: { type: "string" }
        },
        active: { type: "boolean" }
      },
      required: ["name", "age", "occupation"]
    };

    const jsonResponse = await service.getJSONResponse(
      "You are a helpful assistant that creates fictional character profiles.",
      "Create a profile for a software developer named Alex",
      personSchema
    );

    const personData = jsonResponse.parseJSON();
    console.log('JSON Response (should be clean):', JSON.stringify(personData, null, 2));
    
    // Validate the response matches the schema
    const validatedData = jsonResponse.validateJSONSchema(personSchema);
    console.log('Schema validation passed:', !!validatedData);
    console.log('');

    // Test 5: Complex JSON schema
    console.log('5. Testing complex JSON schema...');
    
    const videoAnalysisSchema = {
      type: "object",
      properties: {
        title: { type: "string" },
        summary: { type: "string" },
        key_points: {
          type: "array",
          items: { type: "string" },
          minItems: 3,
          maxItems: 5
        },
        metadata: {
          type: "object",
          properties: {
            duration_estimate: { type: "string" },
            difficulty_level: { 
              type: "string",
              enum: ["beginner", "intermediate", "advanced"]
            },
            topics: {
              type: "array",
              items: { type: "string" }
            }
          },
          required: ["duration_estimate", "difficulty_level"]
        },
        rating: {
          type: "number",
          minimum: 1,
          maximum: 10
        }
      },
      required: ["title", "summary", "key_points", "metadata", "rating"]
    };

    const complexJsonResponse = await service.getJSONResponse(
      "You are a video analysis expert.",
      "Analyze this video content: 'Introduction to Machine Learning - covers basic concepts, algorithms, and practical examples. Duration: 45 minutes.'",
      videoAnalysisSchema
    );

    const analysisData = complexJsonResponse.parseJSON();
    console.log('Complex JSON Response:', JSON.stringify(analysisData, null, 2));
    
    const validatedAnalysis = complexJsonResponse.validateJSONSchema(videoAnalysisSchema);
    console.log('Complex schema validation passed:', !!validatedAnalysis);
    console.log('');

    // Test 6: Simple JSON utility
    console.log('6. Testing simple JSON utility...');
    
    const simpleSchema = {
      type: "object",
      properties: {
        greeting: { type: "string" },
        language: { type: "string" },
        enthusiasm_level: { type: "number", minimum: 1, maximum: 10 }
      },
      required: ["greeting", "language"]
    };

    const simpleJsonResponse = await service.simpleJSON(
      "Create a greeting in French",
      simpleSchema
    );

    const greetingData = simpleJsonResponse.parseJSON();
    console.log('Simple JSON Response:', JSON.stringify(greetingData, null, 2));
    console.log('');

    // Test 7: Error handling
    console.log('7. Testing error handling...');
    
    try {
      // Test with invalid API key
      const invalidService = new RobustOpenAIService({
        apiKey: 'invalid-key',
        defaultModel: 'gpt-4o-mini'
      });
      
      await invalidService.chat([
        { role: "user", content: "Hello" }
      ]);
      
      console.log('ERROR: Should have failed with invalid API key');
    } catch (error) {
      console.log('Correctly caught authentication error:', error.message.includes('authentication') || error.message.includes('API key'));
    }

    try {
      // Test JSON parsing with invalid response (simulate by creating invalid response)
      const invalidResponse = new RobustOpenAIResponse({
        choices: [{ message: { content: 'invalid json content here' }, finish_reason: 'stop' }],
        usage: { prompt_tokens: 10, completion_tokens: 5, total_tokens: 15 }
      });
      
      invalidResponse.parseJSON();
      console.log('ERROR: Should have failed with invalid JSON');
    } catch (error) {
      console.log('Correctly caught JSON parsing error:', error.message.includes('Invalid JSON'));
    }

    console.log('');

  } catch (error) {
    console.error('Test failed:', error.message);
    throw error;
  }
}

// Run the test
async function runTest() {
  try {
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OPENAI_API_KEY environment variable is required');
    }

    console.log('Robust OpenAI Service Test Suite');
    console.log('================================');
    console.log('');

    await testRobustOpenAIService();

    console.log('================================');
    console.log('✅ All tests passed successfully!');
    console.log('');
    console.log('Key features verified:');
    console.log('- Basic chat functionality');
    console.log('- Tool calling with proper argument parsing');
    console.log('- Complete tool workflow execution');
    console.log('- Clean JSON object responses (no cleaning needed)');
    console.log('- JSON schema validation');
    console.log('- Complex nested JSON schemas');
    console.log('- Proper error handling');

  } catch (error) {
    console.error('❌ Test suite failed:', error.message);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTest();
}

module.exports = {
  testRobustOpenAIService,
  runTest
};