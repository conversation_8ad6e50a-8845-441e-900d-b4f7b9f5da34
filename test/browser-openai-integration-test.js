// Browser console test for OpenAI integration
// Run this in the browser console on any YouTube page after loading the extension

async function testOpenAIIntegration() {
  console.log('=== OpenAI Integration Test Suite ===');
  
  // Test 1: Check if services are loaded
  console.log('\n1. Checking service availability...');
  const services = {
    BrowserOpenAIService: typeof BrowserOpenAIService,
    OpenAIVideoAnalyzer: typeof OpenAIVideoAnalyzer
  };
  
  console.log('Service availability:', services);
  
  if (services.BrowserOpenAIService === 'undefined') {
    console.error('❌ BrowserOpenAIService not available');
    return;
  }
  
  if (services.OpenAIVideoAnalyzer === 'undefined') {
    console.error('❌ OpenAIVideoAnalyzer not available');
    return;
  }
  
  console.log('✅ All services are available');

  // Test 2: Check API key availability
  console.log('\n2. Checking API key configuration...');
  try {
    const storage = await browser.storage.local.get(['openai_api_key']);
    if (!storage.openai_api_key) {
      console.warn('⚠️ OpenAI API key not configured. Set it in the extension settings first.');
      return;
    }
    console.log('✅ OpenAI API key is configured');
  } catch (error) {
    console.error('❌ Error checking API key:', error.message);
    return;
  }

  // Test 3: Test basic OpenAI service functionality
  console.log('\n3. Testing basic OpenAI service...');
  try {
    const storage = await browser.storage.local.get(['openai_api_key']);
    const service = new BrowserOpenAIService({
      apiKey: storage.openai_api_key,
      defaultModel: 'gpt-4o-mini'
    });

    const response = await service.withSystem(
      'You are a helpful assistant.',
      'Respond with exactly: "OpenAI integration test successful"'
    );

    const content = response.getContent();
    console.log('Response:', content);
    
    if (content.includes('successful')) {
      console.log('✅ Basic OpenAI service test passed');
    } else {
      console.warn('⚠️ Unexpected response from OpenAI service');
    }

  } catch (error) {
    console.error('❌ Basic OpenAI service test failed:', error.message);
  }

  // Test 4: Test JSON structured output
  console.log('\n4. Testing JSON structured output...');
  try {
    const storage = await browser.storage.local.get(['openai_api_key']);
    const service = new BrowserOpenAIService({
      apiKey: storage.openai_api_key,
      defaultModel: 'gpt-4o-mini'
    });

    const response = await service.withSystemJSON(
      'You are a JSON response generator. Always return valid JSON.',
      'Create a JSON object with fields: test_name (value: "json_test"), status (value: "passed"), timestamp (current timestamp)'
    );

    const jsonData = response.parseJSONContent();
    console.log('JSON Response:', jsonData);
    
    if (jsonData.test_name === 'json_test') {
      console.log('✅ JSON structured output test passed');
    } else {
      console.warn('⚠️ Unexpected JSON structure');
    }

  } catch (error) {
    console.error('❌ JSON structured output test failed:', error.message);
  }

  // Test 5: Test OpenAI Video Analyzer with sample data
  console.log('\n5. Testing OpenAI Video Analyzer...');
  try {
    const storage = await browser.storage.local.get(['openai_api_key']);
    const analyzer = new OpenAIVideoAnalyzer(storage.openai_api_key);

    const sampleVideo = {
      title: 'JavaScript Basics Tutorial',
      transcript: 'Welcome to JavaScript basics. In this video we will learn about variables, functions, and objects. JavaScript is a programming language used for web development.'
    };

    console.log('Testing structured summary generation...');
    const summary = await analyzer.generateStructuredSummary(sampleVideo.title, sampleVideo.transcript);
    console.log('Summary generated:', summary);
    
    if (summary && summary.title && Array.isArray(summary.key_points)) {
      console.log('✅ OpenAI Video Analyzer test passed');
    } else {
      console.warn('⚠️ Unexpected summary structure');
    }

  } catch (error) {
    console.error('❌ OpenAI Video Analyzer test failed:', error.message);
  }

  // Test 6: Test extension message handling for OpenAI analysis
  console.log('\n6. Testing extension message handling...');
  try {
    // This would simulate how the extension processes videos with OpenAI analysis
    console.log('Simulating background script OpenAI analysis...');
    
    // Check if analyzeVideoWithOpenAI function is available in background context
    const testMessage = await browser.runtime.sendMessage({
      action: 'ping'
    });
    
    if (testMessage && testMessage.pong) {
      console.log('✅ Background script communication working');
      console.log('Note: Full OpenAI analysis integration will be tested when processing actual videos');
    } else {
      console.warn('⚠️ Background script communication issue');
    }

  } catch (error) {
    console.error('❌ Extension message handling test failed:', error.message);
  }

  console.log('\n=== OpenAI Integration Test Suite Complete ===');
  console.log('✅ Enhanced OpenAI service with tool calling and structured output is ready!');
}

// Instructions for running the test
console.log(`
=== OpenAI Integration Test Instructions ===

1. Make sure you have configured your OpenAI API key in the extension settings
2. Load this extension in Firefox
3. Navigate to any YouTube page
4. Open the browser console (F12)
5. Run: testOpenAIIntegration()

The test will verify:
- Service availability
- API key configuration  
- Basic OpenAI service functionality
- JSON structured output
- Video analyzer capabilities
- Extension message handling

Run the test now by calling: testOpenAIIntegration()
`);

// Export for console usage
window.testOpenAIIntegration = testOpenAIIntegration;