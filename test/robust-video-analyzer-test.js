const fs = require('fs');

// Load environment variables
const envContent = fs.readFileSync('.env', 'utf8');
const envVars = {};
envContent.split('\n').forEach(line => {
  const [key, value] = line.split('=');
  if (key && value) {
    envVars[key.trim()] = value.trim();
  }
});
process.env.OPENAI_API_KEY = envVars.OPENAI_API_KEY;

// Import the robust video analyzer
const { RobustVideoAnalyzer } = require('../services/robust-video-analyzer.js');

async function testRobustVideoAnalyzer() {
  try {
    console.log('=== Testing Robust Video Analyzer ===\n');

    const analyzer = new RobustVideoAnalyzer(process.env.OPENAI_API_KEY, {
      model: 'gpt-4o-mini'
    });

    // Sample video data for testing
    const sampleVideo = {
      title: 'Complete Guide to React Hooks and State Management',
      transcript: `Welcome to this comprehensive tutorial on React Hooks and state management. 
      In this video, we'll explore useState, useEffect, useContext, and other essential hooks.
      First, let's understand what React Hooks are and why they were introduced to the React ecosystem.
      Hooks allow us to use state and other React features in functional components.
      We'll start with useState, which is the most fundamental hook for managing component state.
      Then we'll move on to useEffect for handling side effects like API calls and subscriptions.
      useContext helps us avoid prop drilling by providing a way to share data across components.
      We'll also cover custom hooks and how to create reusable stateful logic.
      Finally, we'll discuss best practices for state management in larger React applications.
      This tutorial includes practical examples and real-world use cases.
      By the end, you'll have a solid understanding of how to effectively use React Hooks.`
    };

    // Test 1: Extract video metadata using tool calling
    console.log('1. Testing video metadata extraction (Tool Calling)...');
    try {
      const metadata = await analyzer.extractVideoMetadata(sampleVideo.title, sampleVideo.transcript);
      console.log('✅ Metadata extracted successfully:');
      console.log(JSON.stringify(metadata, null, 2));
      
      // Validate required fields
      const requiredFields = ['main_topic', 'key_themes', 'content_type', 'complexity_level'];
      const missingFields = requiredFields.filter(field => !metadata[field]);
      if (missingFields.length === 0) {
        console.log('✅ All required metadata fields present');
      } else {
        console.log('❌ Missing required fields:', missingFields);
      }
    } catch (error) {
      console.log('❌ Metadata extraction failed:', error.message);
    }
    console.log('');

    // Test 2: Generate structured summary using JSON object format
    console.log('2. Testing structured summary generation (JSON Object)...');
    try {
      const summary = await analyzer.generateStructuredSummary(sampleVideo.title, sampleVideo.transcript);
      console.log('✅ Summary generated successfully:');
      console.log(JSON.stringify(summary, null, 2));
      
      // Validate structure
      if (summary.title && Array.isArray(summary.key_points) && summary.detailed_summary) {
        console.log('✅ Summary structure is valid');
        console.log(`📊 Key points count: ${summary.key_points.length}`);
        console.log(`📝 Summary length: ${summary.detailed_summary.length} characters`);
      } else {
        console.log('❌ Invalid summary structure');
      }
    } catch (error) {
      console.log('❌ Summary generation failed:', error.message);
    }
    console.log('');

    // Test 3: Analyze transcript quality using tool calling
    console.log('3. Testing transcript quality analysis (Tool Calling)...');
    try {
      const quality = await analyzer.analyzeTranscriptQuality(sampleVideo.transcript);
      console.log('✅ Quality analysis completed:');
      console.log(JSON.stringify(quality, null, 2));
      
      // Validate quality metrics
      if (quality.quality_score >= 1 && quality.quality_score <= 10 && 
          quality.estimated_accuracy >= 0 && quality.estimated_accuracy <= 100) {
        console.log('✅ Quality metrics are within valid ranges');
      } else {
        console.log('❌ Quality metrics out of range');
      }
    } catch (error) {
      console.log('❌ Quality analysis failed:', error.message);
    }
    console.log('');

    // Test 4: Generate structured article using JSON object format
    console.log('4. Testing structured article generation (JSON Object)...');
    try {
      const article = await analyzer.generateStructuredArticle(sampleVideo.title, sampleVideo.transcript);
      console.log('✅ Article generated successfully:');
      console.log(`📰 Title: ${article.article_title}`);
      console.log(`📄 Sections: ${article.main_sections?.length || 0}`);
      console.log(`🏷️ Tags: ${article.tags?.length || 0}`);
      console.log(`⏱️ Reading time: ${article.reading_time}`);
      
      if (article.main_sections && article.main_sections.length > 0) {
        console.log('📝 Section headings:');
        article.main_sections.forEach((section, index) => {
          console.log(`  ${index + 1}. ${section.heading}`);
        });
      }
      
      // Validate article structure
      if (article.article_title && article.introduction && Array.isArray(article.main_sections) && 
          article.conclusion && Array.isArray(article.tags)) {
        console.log('✅ Article structure is valid');
      } else {
        console.log('❌ Invalid article structure');
      }
    } catch (error) {
      console.log('❌ Article generation failed:', error.message);
    }
    console.log('');

    // Test 5: Quick analysis (subset of features)
    console.log('5. Testing quick analysis...');
    try {
      const quickResult = await analyzer.quickAnalyze(sampleVideo.title, sampleVideo.transcript);
      console.log('✅ Quick analysis completed:');
      console.log(`🎯 Success: ${quickResult.success}`);
      console.log(`📊 Has metadata: ${!!quickResult.metadata}`);
      console.log(`📝 Has summary: ${!!quickResult.summary}`);
      
      if (quickResult.success) {
        console.log('✅ Quick analysis successful');
      } else {
        console.log('❌ Quick analysis failed:', quickResult.error);
      }
    } catch (error) {
      console.log('❌ Quick analysis failed:', error.message);
    }
    console.log('');

    // Test 6: Comprehensive video analysis
    console.log('6. Testing comprehensive video analysis...');
    try {
      const analysis = await analyzer.analyzeVideo(sampleVideo);
      console.log('✅ Comprehensive analysis completed:');
      console.log(`📊 Components analyzed:`);
      console.log(`  - Metadata: ${analysis.metadata ? '✅' : '❌'}`);
      console.log(`  - Summary: ${analysis.summary ? '✅' : '❌'}`);
      console.log(`  - Quality: ${analysis.quality ? '✅' : '❌'}`);
      console.log(`  - Article: ${analysis.article ? '✅' : '❌'}`);
      console.log(`  - Recommendations: ${analysis.recommendations ? '✅' : '❌'}`);
      console.log(`🚨 Errors: ${analysis.errors.length}`);
      
      if (analysis.errors.length > 0) {
        console.log('Error details:');
        analysis.errors.forEach(error => {
          console.log(`  - ${error.task}: ${error.error}`);
        });
      }
      
      // Calculate success rate
      const components = ['metadata', 'summary', 'quality', 'article'];
      const successfulComponents = components.filter(comp => analysis[comp] !== null).length;
      const successRate = (successfulComponents / components.length) * 100;
      console.log(`📈 Success rate: ${successRate.toFixed(1)}%`);
      
      if (successRate >= 75) {
        console.log('✅ Comprehensive analysis mostly successful');
      } else {
        console.log('⚠️ Comprehensive analysis had significant issues');
      }
    } catch (error) {
      console.log('❌ Comprehensive analysis failed:', error.message);
    }
    console.log('');

    // Test 7: Error handling
    console.log('7. Testing error handling...');
    try {
      // Test with empty transcript
      await analyzer.analyzeVideo({ title: 'Test', transcript: '' });
      console.log('❌ Should have failed with empty transcript');
    } catch (error) {
      console.log('✅ Correctly caught empty transcript error:', error.message.includes('required'));
    }

    try {
      // Test with invalid video data
      await analyzer.analyzeVideo({ title: 'Test' });
      console.log('❌ Should have failed with missing transcript');
    } catch (error) {
      console.log('✅ Correctly caught missing transcript error:', error.message.includes('required'));
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    throw error;
  }
}

async function runTest() {
  try {
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OPENAI_API_KEY environment variable is required');
    }

    console.log('Robust Video Analyzer Test Suite');
    console.log('=================================');
    console.log('Using Robust OpenAI Service with Tool Calling and Clean JSON Output');
    console.log('');

    await testRobustVideoAnalyzer();

    console.log('=================================');
    console.log('✅ Robust Video Analyzer Test Suite Completed!');
    console.log('');
    console.log('🎯 Key Features Tested:');
    console.log('  ✅ Tool calling for metadata extraction');
    console.log('  ✅ Tool calling for quality analysis');
    console.log('  ✅ JSON object format for summaries (no cleaning needed)');
    console.log('  ✅ JSON object format for articles (no cleaning needed)');
    console.log('  ✅ JSON schema validation');
    console.log('  ✅ Comprehensive parallel analysis');
    console.log('  ✅ Quick analysis mode');
    console.log('  ✅ Error handling and validation');
    console.log('');
    console.log('🏆 The robust architecture provides clean, reliable results!');

  } catch (error) {
    console.error('❌ Test suite failed:', error.message);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTest();
}

module.exports = {
  testRobustVideoAnalyzer,
  runTest
};