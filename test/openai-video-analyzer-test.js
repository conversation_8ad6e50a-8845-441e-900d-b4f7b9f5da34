const fs = require('fs');

// Load environment variables from .env file
const envContent = fs.readFileSync('.env', 'utf8');
const envVars = {};
envContent.split('\n').forEach(line => {
  const [key, value] = line.split('=');
  if (key && value) {
    envVars[key.trim()] = value.trim();
  }
});
process.env.OPENAI_API_KEY = envVars.OPENAI_API_KEY;

// Import the OpenAI service classes
const OpenAIService = require('../services/openai-completion-service.js');
const { OpenAIVideoAnalyzer } = require('../services/openai-video-analyzer.js');

// Test the enhanced OpenAI service with tool calling and structured output
async function testEnhancedOpenAIService() {
  try {
    console.log('=== Testing Enhanced OpenAI Service ===\n');
    
    const service = new OpenAIService({
      apiKey: process.env.OPENAI_API_KEY,
      defaultModel: 'gpt-4o-mini'
    });

    // Test 1: Basic chat functionality
    console.log('1. Testing basic chat functionality...');
    const basicResponse = await service.withSystem(
      'You are a helpful assistant.',
      'Say hello in a friendly way.'
    );
    console.log('Basic Response:', basicResponse.getContent());
    console.log('Usage:', basicResponse.getUsage());
    console.log('');

    // Test 2: JSON structured output
    console.log('2. Testing JSON structured output...');
    const jsonResponse = await service.withSystemJSON(
      'You are a JSON response generator. Always return valid JSON.',
      'Create a JSON object with fields: greeting, language, and mood for saying hello.'
    );
    const jsonData = jsonResponse.parseJSONContent();
    console.log('JSON Response:', JSON.stringify(jsonData, null, 2));
    console.log('');

    // Test 3: Tool calling
    console.log('3. Testing tool calling...');
    const tools = [
      {
        type: 'function',
        function: {
          name: 'get_weather',
          description: 'Get current weather for a location',
          parameters: {
            type: 'object',
            properties: {
              location: {
                type: 'string',
                description: 'The city and state, e.g. San Francisco, CA'
              },
              unit: {
                type: 'string',
                enum: ['celsius', 'fahrenheit']
              }
            },
            required: ['location']
          }
        }
      }
    ];

    const toolResponse = await service.callWithTools(
      [{ role: 'user', content: 'What is the weather like in Boston?' }],
      tools,
      'auto'
    );

    if (toolResponse.hasToolCalls()) {
      const toolCall = toolResponse.getFirstToolCall();
      console.log('Tool Call:', {
        name: toolCall.function.name,
        arguments: JSON.parse(toolCall.function.arguments)
      });
    } else {
      console.log('No tool calls made. Response:', toolResponse.getContent());
    }
    console.log('');

  } catch (error) {
    console.error('Error testing enhanced OpenAI service:', error.message);
  }
}

// Test the OpenAI Video Analyzer
async function testOpenAIVideoAnalyzer() {
  try {
    console.log('=== Testing OpenAI Video Analyzer ===\n');
    
    const analyzer = new OpenAIVideoAnalyzer(process.env.OPENAI_API_KEY);

    // Sample video data for testing
    const sampleVideo = {
      title: 'How to Build a React App with TypeScript',
      transcript: `Welcome to this tutorial on building React applications with TypeScript. 
      In this video, we'll cover the basics of setting up a new React project with TypeScript support.
      First, we'll install the necessary dependencies including React, TypeScript, and development tools.
      Then we'll create our first TypeScript component and explore type safety features.
      We'll also discuss best practices for structuring your TypeScript React project.
      By the end of this tutorial, you'll have a solid foundation for building type-safe React applications.
      Don't forget to like and subscribe for more programming tutorials!`
    };

    // Test 1: Extract video metadata
    console.log('1. Testing video metadata extraction...');
    try {
      const metadata = await analyzer.extractVideoMetadata(sampleVideo.title, sampleVideo.transcript);
      console.log('Video Metadata:', JSON.stringify(metadata, null, 2));
    } catch (error) {
      console.error('Metadata extraction failed:', error.message);
    }
    console.log('');

    // Test 2: Generate structured summary
    console.log('2. Testing structured summary generation...');
    try {
      const summary = await analyzer.generateStructuredSummary(sampleVideo.title, sampleVideo.transcript);
      console.log('Structured Summary:', JSON.stringify(summary, null, 2));
    } catch (error) {
      console.error('Summary generation failed:', error.message);
    }
    console.log('');

    // Test 3: Analyze transcript quality
    console.log('3. Testing transcript quality analysis...');
    try {
      const quality = await analyzer.analyzeTranscriptQuality(sampleVideo.transcript);
      console.log('Transcript Quality:', JSON.stringify(quality, null, 2));
    } catch (error) {
      console.error('Quality analysis failed:', error.message);
    }
    console.log('');

    // Test 4: Generate structured article
    console.log('4. Testing structured article generation...');
    try {
      const article = await analyzer.generateStructuredArticle(sampleVideo.title, sampleVideo.transcript);
      console.log('Structured Article:', JSON.stringify(article, null, 2));
    } catch (error) {
      console.error('Article generation failed:', error.message);
    }
    console.log('');

    // Test 5: Comprehensive video analysis
    console.log('5. Testing comprehensive video analysis...');
    try {
      const analysis = await analyzer.analyzeVideo(sampleVideo);
      console.log('Comprehensive Analysis Results:');
      console.log('- Metadata:', analysis.metadata ? 'Generated' : 'Failed');
      console.log('- Summary:', analysis.summary ? 'Generated' : 'Failed');
      console.log('- Quality:', analysis.quality ? 'Generated' : 'Failed');
      console.log('- Article:', analysis.article ? 'Generated' : 'Failed');
      console.log('- Errors:', analysis.errors.length);
      
      if (analysis.errors.length > 0) {
        console.log('Errors encountered:');
        analysis.errors.forEach(error => {
          console.log(`  - ${error.task}: ${error.error}`);
        });
      }
    } catch (error) {
      console.error('Comprehensive analysis failed:', error.message);
    }

  } catch (error) {
    console.error('Error testing OpenAI video analyzer:', error.message);
  }
}

// Run all tests
async function runAllTests() {
  try {
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OPENAI_API_KEY environment variable is required');
    }

    console.log('OpenAI Enhanced Service and Video Analyzer Test Suite');
    console.log('='.repeat(60));
    console.log('');

    await testEnhancedOpenAIService();
    await testOpenAIVideoAnalyzer();

    console.log('='.repeat(60));
    console.log('All tests completed!');

  } catch (error) {
    console.error('Test suite failed:', error.message);
    process.exit(1);
  }
}

// Run the tests if this file is executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = {
  testEnhancedOpenAIService,
  testOpenAIVideoAnalyzer,
  runAllTests
};