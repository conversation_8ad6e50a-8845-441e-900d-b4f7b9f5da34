<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Bookmarks</title>
    <link href="https://fonts.googleapis.com/css2?family=Anton&family=Dancing+Script:wght@400;500;600&family=Pacifico&family=Kalam:wght@300;400&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="youtube-logo.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --yt-red: #FF0000;
            --yt-red-hover: #CC0000;
            --yt-red-light: #FFE5E5;
            --bg-primary: #FFFFFF;
            --bg-secondary: #F9F9F9;
            --bg-card: #FFFFFF;
            --text-primary: #0F0F0F;
            --text-secondary: #606060;
            --text-tertiary: #909090;
            --border: #E5E5E5;
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
            --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.15);
            --glass: rgba(255, 255, 255, 0.7);
            --glass-border: rgba(255, 255, 255, 0.5);
        }

        body {
            font-family: 'YouTube Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
            width: 580px;
            min-height: 400px;
        }

        /* Subtle animated background */
        body::before {
            content: '';
            position: fixed;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle at 30% 40%, rgba(255, 0, 0, 0.03) 0%, transparent 50%),
                        radial-gradient(circle at 70% 60%, rgba(255, 0, 0, 0.02) 0%, transparent 50%);
            animation: gentleFloat 30s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes gentleFloat {
            0%, 100% { transform: translate(0, 0) rotate(0deg); }
            33% { transform: translate(30px, -30px) rotate(120deg); }
            66% { transform: translate(-20px, 20px) rotate(240deg); }
        }

        /* Container */
        .container {
            max-width: 580px;
            margin: 0 auto;
            padding: 4px 2px;
        }

        /* Header Card */
        .header {
            background: var(--bg-card);
            border-radius: 10px;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border);
            margin: 4px 2px 8px 2px;
            padding: 12px 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, 
                        transparent 0%, 
                        rgba(255, 0, 0, 0.03) 50%, 
                        transparent 100%);
            opacity: 0;
            transition: opacity 0.2s ease;
            pointer-events: none;
            border-radius: 10px;
        }

        .header:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
            border-color: rgba(255, 0, 0, 0.1);
        }

        .header:hover::before {
            opacity: 1;
        }

        .header h2 {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 4px;
            position: relative;
            z-index: 1;
        }

        .header p {
            color: var(--text-secondary);
            font-size: 11px;
            position: relative;
            z-index: 1;
        }

        /* Video Card Stack */
        .video-stack {
            display: flex;
            flex-direction: column;
            gap: 6px;
            padding: 0 2px;
        }

        /* Video Card */
        .video-card {
            background: var(--bg-card);
            border-radius: 10px;
            overflow: hidden;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-sm);
            position: relative;
            cursor: pointer;
            border: 1px solid var(--border);
            display: flex;
            padding: 8px;
            gap: 8px;
        }

        .video-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, 
                        transparent 0%, 
                        rgba(255, 0, 0, 0.03) 50%, 
                        transparent 100%);
            opacity: 0;
            transition: opacity 0.2s ease;
            pointer-events: none;
        }

        .video-card:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
            border-color: rgba(255, 0, 0, 0.1);
        }

        .video-card:hover::before {
            opacity: 1;
        }

        /* Left column */
        .left-column {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        /* Thumbnail Container */
        .thumbnail-container {
            position: relative;
            width: 240px;
            height: 135px;
            flex-shrink: 0;
            overflow: hidden;
            border-radius: 8px;
        }

        .thumbnail {
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.2s ease;
        }

        .video-card:hover .thumbnail img {
            transform: scale(1.05);
        }

        .duration {
            position: absolute;
            bottom: 3px;
            right: 3px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 1px 3px;
            border-radius: 2px;
            font-size: 9px;
            font-weight: 500;
            backdrop-filter: blur(8px);
            letter-spacing: 0.3px;
        }

        /* Progress indicator */
        .watch-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 2px;
            background: var(--yt-red);
            border-radius: 0 1px 1px 0;
            transition: width 0.2s ease;
        }

        /* Thumbnail Overlay Actions */
        .thumbnail-actions {
            position: absolute;
            top: 3px;
            right: 3px;
            display: flex;
            flex-direction: column;
            gap: 3px;
            opacity: 0;
            transform: translateX(8px);
            transition: all 0.2s ease;
        }

        .video-card:hover .thumbnail-actions {
            opacity: 1;
            transform: translateX(0);
        }

        .quick-action {
            width: 24px;
            height: 24px;
            background: var(--glass);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .quick-action:hover {
            background: white;
            transform: scale(1.05);
        }

        .quick-action svg {
            width: 11px;
            height: 11px;
            fill: var(--text-primary);
        }

        /* Content Section */
        .card-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 5px;
            min-width: 0; /* Allow text truncation */
        }

        /* Title Row */
        .title-row {
            display: flex;
            gap: 6px;
        }

        .channel-avatar {
            width: 22px;
            height: 22px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--yt-red), var(--yt-red-hover));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 9px;
            flex-shrink: 0;
            overflow: hidden;
        }

        .channel-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        /* When avatar has the image class, remove the background */
        .channel-avatar.has-image {
            background: none;
        }

        .title-info {
            flex: 1;
            min-width: 0;
        }

        .video-title {
            font-size: 13px;
            font-weight: 600;
            line-height: 1.2;
            margin-bottom: 2px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            color: var(--text-primary);
        }

        .video-meta {
            display: flex;
            align-items: center;
            gap: 3px;
            font-size: 11px;
            color: var(--text-secondary);
        }

        .channel-name {
            font-weight: 500;
            transition: color 0.2s ease;
        }

        .channel-name:hover {
            color: var(--text-primary);
        }

        .meta-dot {
            width: 2px;
            height: 2px;
            background: var(--text-secondary);
            border-radius: 50%;
            margin: 0 2px;
        }

        /* AI Response Section - Clean with line separator */
        .ai-response {
            margin: 8px 0 0 0;
            padding: 8px 6px 0 12px;
            font-size: 11px;
            line-height: 1.4;
            color: var(--text-secondary);
            font-weight: 400;
            font-style: italic;
            border-top: 1px solid var(--border);
        }

        /* Tags under thumbnail */
        .tags {
            display: flex;
            gap: 3px;
            flex-wrap: wrap;
            width: 240px;
            margin-top: 4px;
        }

        .tag {
            padding: 2px 5px;
            border-radius: 3px;
            font-size: 9px;
            color: white;
            transition: all 0.2s ease;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.3px;
            border: none;
            line-height: 1.2;
        }

        /* Default tag colors */
        .tag[data-category="new"] { background: #7C3AED; } /* Purple */
        .tag[data-category="video"] { background: #10B981; } /* Emerald */

        .tag:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
            filter: brightness(1.15);
        }

        /* Actions Row */
        .actions-row {
            display: flex;
            align-items: center;
            gap: 4px;
            margin-top: auto;
        }

        /* Actions Bar */
        .actions-bar {
            display: flex;
            gap: 4px;
        }

        .action-btn {
            padding: 3px 6px;
            background: var(--bg-secondary);
            border: 1px solid var(--border);
            border-radius: 5px;
            font-size: 10px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 2px;
            color: var(--text-secondary);
        }

        .action-btn:hover {
            background: var(--yt-red);
            color: white;
            border-color: var(--yt-red);
            transform: translateY(-1px);
            box-shadow: 0 1px 4px rgba(255, 0, 0, 0.2);
        }

        .action-btn.active {
            background: var(--yt-red);
            color: white;
            border-color: var(--yt-red);
        }

        .action-btn svg {
            width: 11px;
            height: 11px;
            fill: currentColor;
        }

        /* Empty state */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-secondary);
        }

        .empty-state h3 {
            font-size: 16px;
            margin-bottom: 8px;
            color: var(--text-primary);
        }

        .empty-state p {
            font-size: 14px;
            line-height: 1.4;
        }

        /* Loading state */
        .loading {
            text-align: center;
            padding: 20px;
            color: var(--text-secondary);
        }

        /* Status indicators */
        .status-indicator {
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: 500;
        }

        .status-queued {
            background: #FEF3C7;
            color: #92400E;
        }

        .status-processing {
            background: #DBEAFE;
            color: #1E40AF;
        }

        .status-complete {
            background: #D1FAE5;
            color: #047857;
        }

        .status-error {
            background: #FEE2E2;
            color: #DC2626;
        }

        /* Animations */
        @keyframes ripple {
            to {
                transform: scale(2);
                opacity: 0;
            }
        }

        /* Responsive adjustments - only for very small screens */
        @media (max-width: 320px) {
            body {
                width: 100%;
            }
            
            .container, .header, .video-stack {
                padding-left: 4px;
                padding-right: 4px;
            }
            
            .video-card {
                flex-direction: column;
                padding: 0;
            }

            .thumbnail-container {
                width: 100%;
                height: 0;
                padding-bottom: 56.25%;
                position: relative;
            }

            .thumbnail {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
            }

            .tags {
                width: 100%;
                padding: 4px 8px;
            }

            .card-content {
                padding: 8px;
            }

            .actions-row {
                flex-direction: row;
                align-items: center;
                gap: 4px;
            }

            .actions-bar {
                justify-content: stretch;
                width: 100%;
            }

            .action-btn {
                flex: 1;
            }
        }

        /* Adjustments for smaller thumbnail on narrow screens */
        @media (max-width: 480px) {
            .thumbnail-container {
                width: 180px;
                height: 101px;
            }
            
            .tags {
                width: 180px;
            }
            
            .video-title {
                font-size: 12px;
            }
            
            .video-meta {
                font-size: 10px;
            }
            
            .ai-response {
                font-size: 11px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2 id="header-title" title="Open in new tab">
                <span class="default-text" style="display: none;">YouTube Bookmarks</span>
                <span class="youtube-logo">
                    <span class="you">You</span><span class="tube">Tube</span> <span class="bookmarks-text">Bookmarks</span>
                </span>
            </h2>
            <p>Your saved videos and transcripts</p>
        </div>

        <div id="video-list-container" class="video-stack">
            <!-- The list of videos is loaded here by panel.js -->
        </div>
    </div>

    <script src="panel.js"></script>
</body>
</html>