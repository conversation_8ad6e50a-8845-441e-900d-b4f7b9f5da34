document.addEventListener("DOMContentLoaded", () => {
  console.log("Bookmarks page DOM content loaded.");
  const videoListContainer = document.getElementById("video-list-container");

  // Helper function to get transcript button text based on status
  function getTranscriptButtonText(status) {
    switch (status) {
      case "fetching":
        return "Loading...";
      case "ready":
        return "Listen";
      case "error":
        return "Retry";
      default:
        return "Loading...";
    }
  }

  // Helper function to convert markdown to HTML
  function markdownToHtml(markdown) {
    if (!markdown || typeof markdown !== 'string') {
      return '';
    }
    
    let html = markdown
      // Escape any existing HTML to prevent XSS
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      
      // Headers (## Header -> <h2>Header</h2>)
      .replace(/^### (.*$)/gm, '<h3>$1</h3>')
      .replace(/^## (.*$)/gm, '<h2>$1</h2>')
      .replace(/^# (.*$)/gm, '<h1>$1</h1>')
      
      // Bold text (**text** -> <strong>text</strong>)
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      
      // Italic text (*text* -> <em>text</em>)
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      
      // Inline code (`code` -> <code>code</code>)
      .replace(/`([^`]+)`/g, '<code>$1</code>')
      
      // Links ([text](url) -> <a href="url">text</a>)
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')
      
      // Convert double newlines to paragraphs
      .replace(/\n\n/g, '</p><p>')
      
      // Convert single newlines to line breaks
      .replace(/\n/g, '<br>');
    
    // Handle unordered lists (- item -> <ul><li>item</li></ul>)
    html = html.replace(/^- (.+)$/gm, '<li>$1</li>');
    html = html.replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>');
    
    // Handle numbered lists (1. item -> <ol><li>item</li></ol>)
    html = html.replace(/^\d+\. (.+)$/gm, '<li>$1</li>');
    html = html.replace(/(<li>.*<\/li>)/gs, (match) => {
      if (!match.includes('<ul>')) {
        return `<ol>${match}</ol>`;
      }
      return match;
    });
    
    // Wrap in paragraphs if not already wrapped
    if (!html.startsWith('<')) {
      html = `<p>${html}</p>`;
    }
    
    return html;
  }

  // Helper function to get article preview
  function getArticlePreview(video) {
    if (video.transcriptStatus !== "ready" || !video.article) {
      if (video.transcriptStatus === "fetching") {
        return '<span class="article-preview loading">Generating article...</span>';
      } else if (video.transcriptStatus === "error") {
        return '<span class="article-preview error">Article unavailable</span>';
      }
      return "";
    }
    
    const article = video.article.trim();
    const htmlContent = markdownToHtml(article);
    return `<div class="article-preview">${htmlContent}</div>`;
  }

  function render(videos) {
    console.log("Rendering videos:", videos);
    videoListContainer.innerHTML = "";

    if (!videos || videos.length === 0) {
      videoListContainer.innerHTML =
        '<div class="empty-state"><h3>No videos bookmarked yet</h3><p>Right-click on YouTube videos to bookmark them!</p></div>';
      return;
    }

    videos.forEach((video) => {
      // Get first letter of channel name or title for avatar fallback
      const channelInitial = (video.channelName || video.title || 'V').charAt(0).toUpperCase();
      
      // Determine avatar class and content
      const avatarClass = video.channelAvatar ? 'channel-avatar has-image' : 'channel-avatar';
      const avatarContent = video.channelAvatar 
        ? `<img src="${video.channelAvatar}" alt="Channel avatar">`
        : channelInitial;

      const videoCard = document.createElement("article");
      videoCard.className = "video-card";

      videoCard.innerHTML = `
        <div class="left-column">
          <div class="thumbnail-container">
            <div class="thumbnail">
              <img src="${video.thumbnailUrl}" alt="Video thumbnail" class="clickable" data-url="${video.url}">
              ${video.duration ? `<span class="duration">${video.duration}</span>` : ''}
            </div>
            <div class="thumbnail-actions">
              <button class="quick-action" title="Remove video" data-id="${video.id}">
                <svg viewBox="0 0 20 20">
                  <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414L7.586 12l-1.293 1.293a1 1 0 101.414 1.414L9 13.414l1.293 1.293a1 1 0 001.414-1.414L10.414 12l1.293-1.293z" clip-rule="evenodd"/>
                </svg>
              </button>
            </div>
          </div>
          
          <div class="tags">
            <span class="tag" data-category="new">NEW</span>
            <span class="tag" data-category="video">VIDEO</span>
          </div>
        </div>
        
        <div class="card-content">
          <div class="title-row">
            <div class="${avatarClass}">${avatarContent}</div>
            <div class="title-info">
              <h3 class="video-title clickable" data-url="${video.url}" title="${video.title}">${video.title}</h3>
              <div class="video-meta">
                <span class="channel-name">${video.channelName || 'Unknown Channel'}</span>
                ${video.viewCount ? `<span class="meta-dot"></span><span class="view-count">${video.viewCount}</span>` : ''}
                <span class="meta-dot"></span><span class="upload-time">Added: ${new Date(video.dateAdded).toLocaleDateString()}</span>
              </div>
            </div>
          </div>
          
          ${getArticlePreview(video)}
          
          <div class="actions-row">
            <div class="actions-bar">
              <button class="action-btn transcript-btn transcript-${video.transcriptStatus || 'fetching'}" 
                      data-id="${video.id}" 
                      data-title="${video.title}" 
                      data-status="${video.transcriptStatus || 'fetching'}">
                <svg viewBox="0 0 20 20">
                  <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                  <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 001 1h1a1 1 0 001-1V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 2a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h.01a1 1 0 100-2H10zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h.01a1 1 0 100-2H10z" clip-rule="evenodd"/>
                </svg>
                <span>${getTranscriptButtonText(video.transcriptStatus)}</span>
              </button>
              <button class="action-btn delete-btn" data-id="${video.id}">
                <svg viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"/>
                </svg>
                <span>Delete</span>
              </button>
            </div>
          </div>
        </div>
      `;

      videoListContainer.appendChild(videoCard);
    });

    // Add event listeners for clickable elements (thumbnails and titles)
    document.querySelectorAll(".clickable").forEach((element) => {
      element.addEventListener("click", (e) => {
        const url = e.target.dataset.url;
        browser.tabs.create({ url: url });
      });
    });

    // Add event listeners for transcript buttons
    document.querySelectorAll(".transcript-btn").forEach((btn) => {
      btn.addEventListener("click", async (e) => {
        const videoId = e.target.dataset.id;
        const videoTitle = e.target.dataset.title;
        await playTranscriptAudio(videoId, videoTitle, e.target);
      });
    });

    document.querySelectorAll(".delete-btn").forEach((btn) => {
      btn.addEventListener("click", async (e) => {
        const videoId = e.target.dataset.id;
        await deleteVideo(videoId);
      });
    });
  }

  async function deleteVideo(videoId) {
    try {
      const result = await browser.storage.local.get("videos");
      const videos = result.videos || [];
      const updatedVideos = videos.filter((video) => video.id !== videoId);
      await browser.storage.local.set({ videos: updatedVideos });
      console.log(`Deleted video ${videoId}`);
    } catch (error) {
      console.error("Error deleting video:", error);
    }
  }

  async function playTranscriptAudio(videoId, videoTitle, button) {
    const originalText = button.textContent;
    const status = button.dataset.status;
    
    try {
      // Handle different statuses
      if (status === "fetching") {
        // Still fetching transcript, show message
        button.textContent = "Please wait...";
        button.style.backgroundColor = "orange";
        setTimeout(() => {
          button.textContent = originalText;
          button.style.backgroundColor = "";
        }, 2000);
        return;
      }
      
      if (status === "error") {
        // Retry fetching transcript first
        button.textContent = "Retrying...";
        button.style.backgroundColor = "orange";
        button.disabled = true;
        
        const response = await browser.runtime.sendMessage({
          action: "getTranscript",
          videoId: videoId,
          videoTitle: videoTitle,
        });
        
        if (response?.success !== false) {
          button.textContent = "Success!";
          button.style.backgroundColor = "green";
          // Reload the page to show updated transcript status
          setTimeout(() => {
            location.reload();
          }, 1000);
        } else {
          throw new Error(response?.error || "Retry failed");
        }
        return;
      }

      // For "ready" status, check if audio is available
      const result = await browser.storage.local.get("videos");
      const videos = result.videos || [];
      const video = videos.find(v => v.id === videoId);
      
      if (!video) {
        throw new Error("Video not found in storage");
      }
      
      if (!video.audioData) {
        // No pre-generated audio, generate it now
        button.textContent = "Generating...";
        button.style.backgroundColor = "#ff9800";
        button.disabled = true;
        
        const response = await browser.runtime.sendMessage({
          action: "generateAudioForVideo",
          videoId: videoId,
          videoTitle: video.title,
          transcript: video.transcript,
          article: video.article
        });
        
        if (!response.success) {
          throw new Error(response.error || "Failed to generate audio");
        }
        
        // Reload to get the updated audio data
        setTimeout(() => location.reload(), 500);
        return;
      }
      
      // Play the pre-generated audio
      button.textContent = "Playing...";
      button.style.backgroundColor = "green";
      button.disabled = true;
      
      // Convert base64 PCM to WAV
      const pcmData = Uint8Array.from(atob(video.audioData), c => c.charCodeAt(0));
      const wavData = pcmToWav(pcmData);
      const audioBlob = new Blob([wavData], { type: 'audio/wav' });
      const audioUrl = URL.createObjectURL(audioBlob);
      
      const audio = new Audio(audioUrl);
      
      audio.addEventListener('ended', () => {
        URL.revokeObjectURL(audioUrl);
        button.textContent = originalText;
        button.style.backgroundColor = "";
        button.disabled = false;
        console.log("[YT-Summarizer] Audio playback completed");
      });
      
      audio.addEventListener('error', (e) => {
        URL.revokeObjectURL(audioUrl);
        button.textContent = "Audio Error!";
        button.style.backgroundColor = "red";
        button.disabled = false;
        console.error("[YT-Summarizer] Audio playback error:", e);
        
        setTimeout(() => {
          button.textContent = originalText;
          button.style.backgroundColor = "";
        }, 3000);
      });
      
      await audio.play();
      console.log("[YT-Summarizer] Audio playback started");
      
    } catch (error) {
      console.error("[YT-Summarizer] Error generating audio:", error);
      
      // Error feedback
      button.textContent = "Error!";
      button.style.backgroundColor = "red";
      button.disabled = false;
      
      setTimeout(() => {
        button.textContent = originalText;
        button.style.backgroundColor = "";
      }, 3000);
    }
  }

  function loadAndRenderVideos() {
    console.log("Loading and rendering videos from storage.");
    browser.storage.local.get("videos").then(
      (result) => {
        console.log("Loaded videos from storage:", result.videos);
        const videos = result.videos || [];
        render(videos);
      },
      (error) => {
        console.error("Error loading videos from storage:", error);
      },
    );
  }

  browser.storage.onChanged.addListener((changes, area) => {
    console.log("Storage changed in area:", area);
    if (area === "local" && changes.videos) {
      console.log(
        "'videos' in storage changed. New value:",
        changes.videos.newValue,
      );
      render(changes.videos.newValue || []);
    }
  });

  loadAndRenderVideos();
});

// Helper function to convert PCM to WAV format
function pcmToWav(pcmData, sampleRate = 24000, numChannels = 1, bitsPerSample = 16) {
  const byteRate = sampleRate * numChannels * (bitsPerSample / 8);
  const blockAlign = numChannels * (bitsPerSample / 8);
  const dataSize = pcmData.length;
  
  const buffer = new ArrayBuffer(44 + dataSize);
  const view = new DataView(buffer);
  
  // WAV header
  const writeString = (offset, string) => {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i));
    }
  };
  
  writeString(0, 'RIFF');
  view.setUint32(4, 36 + dataSize, true);
  writeString(8, 'WAVE');
  writeString(12, 'fmt ');
  view.setUint32(16, 16, true); // fmt chunk size
  view.setUint16(20, 1, true); // audio format (1 = PCM)
  view.setUint16(22, numChannels, true);
  view.setUint32(24, sampleRate, true);
  view.setUint32(28, byteRate, true);
  view.setUint16(32, blockAlign, true);
  view.setUint16(34, bitsPerSample, true);
  writeString(36, 'data');
  view.setUint32(40, dataSize, true);
  
  // Copy PCM data
  const wavData = new Uint8Array(buffer);
  wavData.set(pcmData, 44);
  
  return wavData;
}