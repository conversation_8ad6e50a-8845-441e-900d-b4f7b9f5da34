document.addEventListener('DOMContentLoaded', () => {
    const form = document.getElementById('api-key-form');
    const apifyKeyInput = document.getElementById('apify-key');
    const openaiKeyInput = document.getElementById('openai-key');
    const deepseekKeyInput = document.getElementById('deepseek-key');
    const envImportInput = document.getElementById('env-import');
    const envImportToggle = document.getElementById('env-import-toggle');
    const envImportContainer = document.getElementById('env-import-container');
    const saveBtn = document.getElementById('save-btn');
    const skipBtn = document.getElementById('skip-btn');
    const apifyError = document.getElementById('apify-error');
    const openaiError = document.getElementById('openai-error');
    const deepseekError = document.getElementById('deepseek-error');
    const successMessage = document.getElementById('success-message');

    // Check if API keys already exist
    browser.storage.local.get(['apify_api_key', 'openai_api_key', 'deepseek_api_key']).then(result => {
        if (result.apify_api_key) {
            apifyKeyInput.value = result.apify_api_key;
        }
        if (result.openai_api_key) {
            openaiKeyInput.value = result.openai_api_key;
        }
        if (result.deepseek_api_key) {
            deepseekKeyInput.value = result.deepseek_api_key;
        }
    });

    // Show .env import section (one-way toggle)
    envImportToggle.addEventListener('click', () => {
        envImportContainer.style.display = 'block';
        envImportToggle.style.display = 'none';
        envImportInput.focus();
    });

    function showApifyError(message) {
        apifyError.textContent = message;
        apifyError.style.display = 'block';
    }
    
    function showOpenAIError(message) {
        openaiError.textContent = message;
        openaiError.style.display = 'block';
    }
    
    function showDeepSeekError(message) {
        deepseekError.textContent = message;
        deepseekError.style.display = 'block';
    }

    function showSuccess() {
        successMessage.style.display = 'block';
        apifyError.style.display = 'none';
        openaiError.style.display = 'none';
        deepseekError.style.display = 'none';
    }

    function hideMessages() {
        apifyError.style.display = 'none';
        openaiError.style.display = 'none';
        deepseekError.style.display = 'none';
        successMessage.style.display = 'none';
    }

    function validateApifyKey(key) {
        if (!key) {
            return 'Apify API token is required';
        }
        
        if (!key.startsWith('apify_api_')) {
            return 'Apify token should start with "apify_api_"';
        }
        
        if (key.length < 20) {
            return 'Apify token appears to be too short';
        }
        
        return null;
    }
    
    function validateOpenAIKey(key) {
        if (!key) {
            return 'OpenAI API key is required';
        }
        
        if (!key.startsWith('sk-')) {
            return 'OpenAI key should start with "sk-"';
        }
        
        if (key.length < 20) {
            return 'OpenAI key appears to be too short';
        }
        
        return null;
    }
    
    function validateDeepSeekKey(key) {
        if (!key) {
            return 'DeepSeek API key is required';
        }
        
        if (!key.startsWith('sk-')) {
            return 'DeepSeek key should start with "sk-"';
        }
        
        if (key.length < 20) {
            return 'DeepSeek key appears to be too short';
        }
        
        return null;
    }

    function parseEnvContent(envContent) {
        const result = { apify_api_key: null, openai_api_key: null, deepseek_api_key: null };
        if (!envContent.trim()) return result;
        
        const lines = envContent.split('\n');
        for (const line of lines) {
            const trimmedLine = line.trim();
            if (!trimmedLine || trimmedLine.startsWith('#')) continue;
            
            const [key, ...valueParts] = trimmedLine.split('=');
            if (!key || valueParts.length === 0) continue;
            
            const value = valueParts.join('=').replace(/^["']|["']$/g, '');
            
            if (key.trim() === 'APIFY_API_KEY') {
                result.apify_api_key = value;
            } else if (key.trim() === 'OPENAI_API_KEY') {
                result.openai_api_key = value;
            } else if (key.trim() === 'DEEPSEEK_API_KEY') {
                result.deepseek_api_key = value;
            }
        }
        
        return result;
    }

    async function testApifyKey(key) {
        try {
            const response = await fetch(`https://api.apify.com/v2/users/me?token=${key}`, {
                method: 'GET'
            });

            if (response.status === 401) {
                throw new Error('Invalid Apify API token');
            } else if (!response.ok) {
                throw new Error(`Apify API test failed: ${response.status}`);
            }

            return true;
        } catch (error) {
            throw new Error(`Apify token validation failed: ${error.message}`);
        }
    }
    
    async function testOpenAIKey(key) {
        try {
            const response = await fetch('https://api.openai.com/v1/models', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${key}`
                }
            });

            if (response.status === 401) {
                throw new Error('Invalid OpenAI API key');
            } else if (response.status === 429) {
                throw new Error('OpenAI rate limit exceeded');
            } else if (!response.ok) {
                throw new Error(`OpenAI API test failed: ${response.status}`);
            }

            return true;
        } catch (error) {
            throw new Error(`OpenAI key validation failed: ${error.message}`);
        }
    }
    
    async function testDeepSeekKey(key) {
        try {
            const response = await fetch('https://api.deepseek.com/v1/models', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${key}`
                }
            });

            if (response.status === 401) {
                throw new Error('Invalid DeepSeek API key');
            } else if (response.status === 429) {
                throw new Error('DeepSeek rate limit exceeded');
            } else if (!response.ok) {
                throw new Error(`DeepSeek API test failed: ${response.status}`);
            }

            return true;
        } catch (error) {
            throw new Error(`DeepSeek key validation failed: ${error.message}`);
        }
    }

    form.addEventListener('submit', async (e) => {
        e.preventDefault();
        hideMessages();

        // First check if .env content is provided
        const envContent = envImportInput.value.trim();
        let apifyKey = apifyKeyInput.value.trim();
        let openaiKey = openaiKeyInput.value.trim();
        let deepseekKey = deepseekKeyInput.value.trim();
        
        if (envContent) {
            const parsedEnv = parseEnvContent(envContent);
            if (parsedEnv.apify_api_key) {
                apifyKey = parsedEnv.apify_api_key;
            }
            if (parsedEnv.openai_api_key) {
                openaiKey = parsedEnv.openai_api_key;
            }
            if (parsedEnv.deepseek_api_key) {
                deepseekKey = parsedEnv.deepseek_api_key;
            }
        }
        
        // Validate formats
        const apifyValidationError = validateApifyKey(apifyKey);
        const openaiValidationError = validateOpenAIKey(openaiKey);
        const deepseekValidationError = validateDeepSeekKey(deepseekKey);
        
        if (apifyValidationError) {
            showApifyError(apifyValidationError);
            return;
        }
        
        if (openaiValidationError) {
            showOpenAIError(openaiValidationError);
            return;
        }
        
        if (deepseekValidationError) {
            showDeepSeekError(deepseekValidationError);
            return;
        }

        // Disable button and show loading
        saveBtn.disabled = true;
        saveBtn.textContent = 'Testing Keys...';

        try {
            // Test all API keys
            await testApifyKey(apifyKey);
            await testOpenAIKey(openaiKey);
            await testDeepSeekKey(deepseekKey);

            // Save to storage
            await browser.storage.local.set({ 
                apify_api_key: apifyKey,
                openai_api_key: openaiKey,
                deepseek_api_key: deepseekKey,
                api_keys_configured: true
            });

            showSuccess();
            saveBtn.textContent = 'Saved!';
            
            // Open YouTube page and close the setup page after a delay
            setTimeout(() => {
                browser.tabs.create({ url: 'https://www.youtube.com' });
                window.close();
            }, 1500);

        } catch (error) {
            // Show error in appropriate field based on error message
            if (error.message.includes('Apify')) {
                showApifyError(error.message);
            } else if (error.message.includes('OpenAI')) {
                showOpenAIError(error.message);
            } else if (error.message.includes('DeepSeek')) {
                showDeepSeekError(error.message);
            } else {
                showApifyError(error.message);
            }
            saveBtn.disabled = false;
            saveBtn.textContent = 'Save Keys';
        }
    });

    skipBtn.addEventListener('click', async () => {
        // Mark as skipped so we don't show this again immediately
        await browser.storage.local.set({ 
            api_key_setup_skipped: true 
        });
        browser.tabs.create({ url: 'https://www.youtube.com' });
        window.close();
    });

    // Auto-populate individual fields when .env content is pasted
    envImportInput.addEventListener('input', () => {
        hideMessages();
        const envContent = envImportInput.value.trim();
        if (envContent) {
            const parsedEnv = parseEnvContent(envContent);
            if (parsedEnv.apify_api_key && !apifyKeyInput.value.trim()) {
                apifyKeyInput.value = parsedEnv.apify_api_key;
            }
            if (parsedEnv.openai_api_key && !openaiKeyInput.value.trim()) {
                openaiKeyInput.value = parsedEnv.openai_api_key;
            }
            if (parsedEnv.deepseek_api_key && !deepseekKeyInput.value.trim()) {
                deepseekKeyInput.value = parsedEnv.deepseek_api_key;
            }
        }
    });

    // Clear messages when user starts typing
    apifyKeyInput.addEventListener('input', hideMessages);
    openaiKeyInput.addEventListener('input', hideMessages);
    deepseekKeyInput.addEventListener('input', hideMessages);
});