<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>YouTube Bookmarks</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Anton&family=Dancing+Script:wght@400;500;600&family=Pacifico&family=Kalam:wght@300;400&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="youtube-logo.css" />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      :root {
        --yt-red: #ff0000;
        --yt-red-hover: #cc0000;
        --yt-red-light: #ffe5e5;
        --bg-primary: #ffffff;
        --bg-secondary: #f9f9f9;
        --bg-card: #ffffff;
        --text-primary: #0f0f0f;
        --text-secondary: #606060;
        --text-tertiary: #909090;
        --border: #e5e5e5;
        --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
        --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
        --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.15);
        --glass: rgba(255, 255, 255, 0.7);
        --glass-border: rgba(255, 255, 255, 0.5);
      }

      body {
        font-family: "YouTube Sans", -apple-system, BlinkMacSystemFont,
          "Segoe UI", Roboto, sans-serif;
        background: var(--bg-secondary);
        color: var(--text-primary);
        line-height: 1.5;
        -webkit-font-smoothing: antialiased;
        min-height: 100vh;
      }

      /* Subtle animated background */
      body::before {
        content: "";
        position: fixed;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(
            circle at 30% 40%,
            rgba(255, 0, 0, 0.03) 0%,
            transparent 50%
          ),
          radial-gradient(
            circle at 70% 60%,
            rgba(255, 0, 0, 0.02) 0%,
            transparent 50%
          );
        animation: gentleFloat 30s ease-in-out infinite;
        z-index: -1;
      }

      @keyframes gentleFloat {
        0%,
        100% {
          transform: translate(0, 0) rotate(0deg);
        }
        33% {
          transform: translate(30px, -30px) rotate(120deg);
        }
        66% {
          transform: translate(-20px, 20px) rotate(240deg);
        }
      }

      /* Container */
      .page-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      /* Header Card */
      .header {
        background: var(--bg-card);
        border-radius: 10px;
        box-shadow: var(--shadow-sm);
        border: 1px solid var(--border);
        margin-bottom: 20px;
        padding: 20px;
        text-align: center;
        cursor: pointer;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
      }

      .header::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          transparent 0%,
          rgba(255, 0, 0, 0.03) 50%,
          transparent 100%
        );
        opacity: 0;
        transition: opacity 0.2s ease;
        pointer-events: none;
        border-radius: 10px;
      }

      .header:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
        border-color: rgba(255, 0, 0, 0.1);
      }

      .header:hover::before {
        opacity: 1;
      }

      .header h2 {
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 4px;
        position: relative;
        z-index: 1;
      }

      /* Video Card Stack */
      .video-stack {
        display: flex;
        flex-direction: column;
        gap: 16px;
      }

      /* Video Card - Full Width Layout */
      .video-card {
        background: var(--bg-card);
        border-radius: 10px;
        overflow: hidden;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: var(--shadow-sm);
        position: relative;
        cursor: pointer;
        border: 1px solid var(--border);
        display: flex;
        padding: 16px;
        gap: 16px;
      }

      .video-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          transparent 0%,
          rgba(255, 0, 0, 0.03) 50%,
          transparent 100%
        );
        opacity: 0;
        transition: opacity 0.2s ease;
        pointer-events: none;
      }

      .video-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        border-color: rgba(255, 0, 0, 0.1);
      }

      .video-card:hover::before {
        opacity: 1;
      }

      /* Left column */
      .left-column {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      /* Thumbnail Container - Larger for full page */
      .thumbnail-container {
        position: relative;
        width: 320px;
        height: 180px;
        flex-shrink: 0;
        overflow: hidden;
        border-radius: 8px;
      }

      .thumbnail {
        width: 100%;
        height: 100%;
        overflow: hidden;
      }

      .thumbnail img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.2s ease;
      }

      .video-card:hover .thumbnail img {
        transform: scale(1.05);
      }

      .duration {
        position: absolute;
        bottom: 4px;
        right: 4px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 2px 4px;
        border-radius: 3px;
        font-size: 11px;
        font-weight: 500;
        backdrop-filter: blur(8px);
        letter-spacing: 0.3px;
      }

      /* Progress indicator */
      .watch-progress {
        position: absolute;
        bottom: 0;
        left: 0;
        height: 3px;
        background: var(--yt-red);
        border-radius: 0 2px 2px 0;
        transition: width 0.2s ease;
      }

      /* Thumbnail Overlay Actions */
      .thumbnail-actions {
        position: absolute;
        top: 4px;
        right: 4px;
        display: flex;
        flex-direction: column;
        gap: 4px;
        opacity: 0;
        transform: translateX(8px);
        transition: all 0.2s ease;
      }

      .video-card:hover .thumbnail-actions {
        opacity: 1;
        transform: translateX(0);
      }

      .quick-action {
        width: 28px;
        height: 28px;
        background: var(--glass);
        backdrop-filter: blur(10px);
        border: 1px solid var(--glass-border);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .quick-action:hover {
        background: white;
        transform: scale(1.05);
      }

      .quick-action svg {
        width: 13px;
        height: 13px;
        fill: var(--text-primary);
      }

      /* Content Section */
      .card-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;
        min-width: 0; /* Allow text truncation */
        min-height: 0; /* Allow proper flex behavior */
      }

      /* Title Row */
      .title-row {
        display: flex;
        gap: 8px;
      }

      .title-info {
        flex: 1;
        min-width: 0;
      }

      .video-title {
        font-size: 16px;
        font-weight: 600;
        line-height: 1.3;
        margin-bottom: 4px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        color: var(--text-primary);
      }

      .video-meta {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 13px;
        color: var(--text-secondary);
      }

      .channel-name {
        font-weight: 500;
        transition: color 0.2s ease;
      }

      .channel-name:hover {
        color: var(--text-primary);
      }

      .meta-dot {
        width: 2px;
        height: 2px;
        background: var(--text-secondary);
        border-radius: 50%;
        margin: 0 2px;
      }

      /* AI Response Section - Clean with line separator (like panel) */
      .ai-response {
        margin: 8px 0 0 0;
        padding: 8px 6px 0 0px;
        font-size: 11px;
        line-height: 1.4;
        color: var(--text-secondary);
        font-weight: 400;
        font-style: italic;
        border-top: 1px solid var(--border);
      }

      /* Article Section - Full article content */
      .article-section {
        margin: 12px 0 0 0;
        padding: 12px 8px 16px 32px;
        font-size: 13px;
        line-height: 1.5;
        color: var(--text-primary);
        border-top: 1px solid var(--border);
      }

      .article-loading,
      .article-error {
        color: var(--text-secondary);
        font-style: italic;
      }

      .article-error {
        color: #dc2626;
      }

      /* Tags under thumbnail */
      .tags {
        display: flex;
        gap: 4px;
        flex-wrap: wrap;
        width: 320px;
        margin-top: 8px;
      }

      .tag {
        padding: 3px 6px;
        border-radius: 4px;
        font-size: 10px;
        color: white;
        transition: all 0.2s ease;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        border: none;
        line-height: 1.2;
      }

      /* Default tag colors */
      .tag[data-category="new"] {
        background: #7c3aed;
      } /* Purple */
      .tag[data-category="video"] {
        background: #10b981;
      } /* Emerald */

      .tag:hover {
        transform: scale(1.05);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
        filter: brightness(1.15);
      }

      /* Actions Row */
      .actions-row {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-top: auto;
      }

      /* Actions Bar */
      .actions-bar {
        display: flex;
        gap: 6px;
      }

      .action-btn {
        padding: 4px 8px;
        background: var(--bg-secondary);
        border: 1px solid var(--border);
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 3px;
        color: var(--text-secondary);
      }

      .action-btn:hover {
        background: var(--yt-red);
        color: white;
        border-color: var(--yt-red);
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(255, 0, 0, 0.2);
      }

      .action-btn.active {
        background: var(--yt-red);
        color: white;
        border-color: var(--yt-red);
      }

      .action-btn svg {
        width: 13px;
        height: 13px;
        fill: currentColor;
      }

      /* Empty state */
      .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: var(--text-secondary);
      }

      .empty-state h3 {
        font-size: 20px;
        margin-bottom: 12px;
        color: var(--text-primary);
      }

      .empty-state p {
        font-size: 16px;
        line-height: 1.4;
      }

      /* Loading state */
      .loading {
        text-align: center;
        padding: 40px;
        color: var(--text-secondary);
      }

      /* Status indicators */
      .status-indicator {
        font-size: 11px;
        padding: 3px 8px;
        border-radius: 4px;
        font-weight: 500;
      }

      .status-queued {
        background: #fef3c7;
        color: #92400e;
      }

      .status-processing {
        background: #dbeafe;
        color: #1e40af;
      }

      .status-complete {
        background: #d1fae5;
        color: #047857;
      }

      .status-error {
        background: #fee2e2;
        color: #dc2626;
      }

      /* Animations */
      @keyframes ripple {
        to {
          transform: scale(2);
          opacity: 0;
        }
      }

      /* Responsive adjustments */
      @media (max-width: 768px) {
        .page-container {
          padding: 12px;
        }

        .video-card {
          flex-direction: column;
          padding: 12px;
        }

        .thumbnail-container {
          width: 100%;
          height: 0;
          padding-bottom: 56.25%;
          position: relative;
        }

        .thumbnail {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
        }

        .tags {
          width: 100%;
          padding: 8px 0;
        }

        .card-content {
          padding: 0;
        }

        .video-title {
          font-size: 14px;
        }

        .video-meta {
          font-size: 12px;
        }

        .ai-response {
          font-size: 12px;
        }
      }
    </style>
  </head>
  <body>
    <div class="page-container">
      <div class="header">
        <h2 id="header-title" title="Open in new tab">
          <span class="default-text" style="display: none"
            >YouTube Bookmarks</span
          >
          <span class="youtube-logo">
            <span class="you">You</span><span class="tube">Tube</span>
            <span class="bookmarks-text">Bookmarks</span>
          </span>
        </h2>
      </div>

      <div id="video-list-container" class="video-stack">
        <!-- The list of videos is loaded here by bookmarks.js -->
      </div>
    </div>
    <script src="bookmarks.js"></script>
  </body>
</html>
