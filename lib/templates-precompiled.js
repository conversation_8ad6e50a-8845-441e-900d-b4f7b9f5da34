(function() {(window.nunjucksPrecompiled = window.nunjucksPrecompiled || {})["investigative-reporter.njk"] = (function() {
function root(env, context, frame, runtime, cb) {
var lineno = 0;
var colno = 0;
var output = "";
try {
var parentTemplate = null;
output += "VIDEO TITLE: ";
output += runtime.suppressValue(runtime.contextOrFrameLookup(context, frame, "title"), env.opts.autoescape);
output += "\r\nVIDEO DURATION: ";
output += runtime.suppressValue(runtime.contextOrFrameLookup(context, frame, "duration"), env.opts.autoescape);
output += "\r\n\r\nTITLE RESPONSE (Already visible to reader): \r\n";
output += runtime.suppressValue(runtime.contextOrFrameLookup(context, frame, "title_response"), env.opts.autoescape);
output += "\r\n\r\nCLEANED TRANSCRIPT:\r\n";
output += runtime.suppressValue(runtime.contextOrFrameLookup(context, frame, "cleaned_transcript"), env.opts.autoescape);
output += "\r\n\r\nGenerate a maximum-density technical segment. Choose length (150/250/350 words) based on information complexity. Pack maximum signal per word while maintaining logical flow.";
if(parentTemplate) {
parentTemplate.rootRenderFunc(env, context, frame, runtime, cb);
} else {
cb(null, output);
}
;
} catch (e) {
  cb(runtime.handleError(e, lineno, colno));
}
}
return {
root: root
};

})();
})();
(function() {(window.nunjucksPrecompiled = window.nunjucksPrecompiled || {})["title-response.njk"] = (function() {
function root(env, context, frame, runtime, cb) {
var lineno = 0;
var colno = 0;
var output = "";
try {
var parentTemplate = null;
output += "VIDEO TITLE: ";
output += runtime.suppressValue(runtime.contextOrFrameLookup(context, frame, "title"), env.opts.autoescape);
output += "\r\n\r\nTRANSCRIPT: ";
output += runtime.suppressValue(runtime.contextOrFrameLookup(context, frame, "transcript"), env.opts.autoescape);
output += "\r\n\r\nWhat's the answer viewers clicked to discover? Give them the exact payoff from the transcript.";
if(parentTemplate) {
parentTemplate.rootRenderFunc(env, context, frame, runtime, cb);
} else {
cb(null, output);
}
;
} catch (e) {
  cb(runtime.handleError(e, lineno, colno));
}
}
return {
root: root
};

})();
})();
(function() {(window.nunjucksPrecompiled = window.nunjucksPrecompiled || {})["transcript-cleaner.njk"] = (function() {
function root(env, context, frame, runtime, cb) {
var lineno = 0;
var colno = 0;
var output = "";
try {
var parentTemplate = null;
output += "Reorganize this transcript for clarity and efficiency while preserving EVERY piece of information. This is lossless compression - remove only redundancy and speech inefficiencies, not content. Group related information together logically.\n\nAfter reading your version, I should be able to answer any question about what was said in the original.\n\nRaw Transcript:\n---\n";
output += runtime.suppressValue(runtime.contextOrFrameLookup(context, frame, "transcript"), env.opts.autoescape);
output += "\n---\n\nProvide the reorganized version with all information intact.";
if(parentTemplate) {
parentTemplate.rootRenderFunc(env, context, frame, runtime, cb);
} else {
cb(null, output);
}
;
} catch (e) {
  cb(runtime.handleError(e, lineno, colno));
}
}
return {
root: root
};

})();
})();
