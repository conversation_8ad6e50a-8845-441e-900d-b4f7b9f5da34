// Context menu management for YouTube video bookmarker
// Handles context menu creation, event handling, and video processing

// Track if context menu has been created
let contextMenuCreated = false;

// Track ongoing video processing to prevent duplicates
const processingVideos = new Set();

/**
 * Create context menus with error handling
 * @returns {boolean} Success status
 */
function createContextMenus() {
  // Only create if not already created
  if (contextMenuCreated) {
    console.log('[YT-Bookmarker] Context menu already exists, skipping creation');
    return true;
  }

  // Create a single comprehensive menu that works in all contexts
  const menuConfig = {
    id: 'bookmark-youtube-video',
    title: 'Bookmark YouTube Video',
    contexts: ['page', 'link', 'image', 'video', 'frame'],
    documentUrlPatterns: ['*://*.youtube.com/*']
  };

  try {
    browser.contextMenus.create(menuConfig, () => {
      if (browser.runtime.lastError) {
        console.error(
          '[YT-Bookmarker] Error creating context menu:',
          browser.runtime.lastError.message
        );
        return false;
      } else {
        contextMenuCreated = true;
        console.log('[YT-Bookmarker] Context menu created successfully');
        return true;
      }
    });
    return true;
  } catch (error) {
    console.error('[YT-Bookmarker] Exception creating context menu:', error.message);
    return false;
  }
}

/**
 * Check if context menu is created
 * @returns {boolean} True if created
 */
function isContextMenuCreated() {
  return contextMenuCreated;
}

/**
 * Reset context menu creation flag (for testing)
 */
function resetContextMenuState() {
  contextMenuCreated = false;
}

/**
 * Check if video is currently being processed
 * @param {string} videoId - Video ID to check
 * @returns {boolean} True if processing
 */
function isVideoProcessing(videoId) {
  return processingVideos.has(videoId);
}

/**
 * Mark video as processing
 * @param {string} videoId - Video ID
 */
function markVideoAsProcessing(videoId) {
  processingVideos.add(videoId);
  
  // Auto-cleanup after timeout to prevent permanent locks
  setTimeout(() => {
    processingVideos.delete(videoId);
  }, 30000); // 30 second timeout
}

/**
 * Mark video as no longer processing
 * @param {string} videoId - Video ID
 */
function markVideoAsComplete(videoId) {
  processingVideos.delete(videoId);
}

/**
 * Get currently processing videos (for debugging)
 * @returns {Set} Set of processing video IDs
 */
function getProcessingVideos() {
  return new Set(processingVideos);
}

/**
 * Clear all processing videos (for testing/reset)
 */
function clearProcessingVideos() {
  processingVideos.clear();
}

/**
 * Extract video ID from context menu info
 * @param {Object} info - Context menu info
 * @param {Object} tab - Tab info  
 * @returns {Promise<Object>} Object with videoId and videoUrl
 */
async function extractVideoIdFromContextMenu(info, tab) {
  let videoUrl = null;
  let videoId = null;

  // Try all possible sources to extract video ID
  const possibleUrls = [
    info.linkUrl,
    info.srcUrl, 
    info.pageUrl,
    tab.url
  ].filter(Boolean);

  // Import video processor utility
  const { getVideoId } = typeof VideoProcessor !== 'undefined' 
    ? VideoProcessor 
    : require('../lib/video-processor.js');

  for (const url of possibleUrls) {
    videoId = getVideoId(url);
    if (videoId) {
      videoUrl = url;
      break;
    }
  }

  // If still no video ID, try DOM extraction via content script
  if (!videoId && tab.url && tab.url.includes('youtube.com')) {
    try {
      // Make sure content script is injected first
      await browser.tabs.executeScript(tab.id, {
        file: 'content/content.js',
        runAt: 'document_idle'
      }).catch(() => {});

      const domVideoId = await browser.tabs.sendMessage(tab.id, {
        action: 'getVideoIdFromClickContext',
        clickInfo: {
          srcUrl: info.srcUrl,
          pageUrl: info.pageUrl,
          linkUrl: info.linkUrl,
          menuItemId: info.menuItemId
        }
      });
      
      if (domVideoId) {
        videoId = domVideoId;
        videoUrl = `https://www.youtube.com/watch?v=${videoId}`;
      }
    } catch (error) {
      console.log('[YT-Bookmarker] DOM extraction failed:', error.message);
    }
  }

  return { videoId, videoUrl };
}

/**
 * Get video title from tab or content script
 * @param {Object} tab - Tab object
 * @param {string} videoId - Video ID
 * @returns {Promise<string>} Video title
 */
async function getVideoTitle(tab, videoId) {
  let title = `YouTube Video - ${videoId}`; // Default placeholder title

  // Try to fetch the title from tab title if we're on a watch page
  if (tab.url && tab.url.includes('/watch?v=' + videoId)) {
    // We're on the actual video page, use the tab title
    if (tab.title && tab.title !== 'YouTube') {
      title = tab.title.replace(' - YouTube', '').trim();
    }
  } else {
    // We're not on the video page, try to get title from content script
    try {
      // Make sure content script is loaded
      await browser.tabs.executeScript(tab.id, {
        file: 'content/content.js',
        runAt: 'document_idle'
      }).catch(() => {}); // Ignore if already injected

      const fetchedTitle = await browser.tabs.sendMessage(tab.id, {
        action: 'getVideoTitleForId',
        videoId: videoId
      });
      
      if (fetchedTitle && fetchedTitle !== 'Title not found') {
        title = fetchedTitle;
      }
    } catch (error) {
      console.log(`[YT-Bookmarker] Could not fetch title for ${videoId}:`, error.message);
    }
  }

  return title;
}

/**
 * Get video metadata from content script
 * @param {Object} tab - Tab object
 * @param {string} videoId - Video ID
 * @returns {Promise<Object>} Video metadata
 */
async function getVideoMetadata(tab, videoId) {
  const defaultMetadata = {
    duration: null,
    channelName: null,
    channelAvatar: null,
    viewCount: null,
    uploadTime: null
  };

  try {
    // Ensure content script is loaded
    await browser.tabs.executeScript(tab.id, {
      file: 'content/content.js',
      runAt: 'document_idle'
    }).catch(() => {}); // Ignore if already injected

    const fetchedMetadata = await browser.tabs.sendMessage(tab.id, {
      action: 'getVideoMetadata',
      videoId: videoId
    });

    if (fetchedMetadata) {
      return {
        duration: fetchedMetadata.duration || null,
        channelName: fetchedMetadata.channelName || null,
        channelAvatar: fetchedMetadata.channelAvatar || null,
        viewCount: fetchedMetadata.viewCount || null,
        uploadTime: fetchedMetadata.uploadTime || null
      };
    }
  } catch (error) {
    console.log(`[YT-Bookmarker] Could not fetch metadata for ${videoId}:`, error.message);
  }

  return defaultMetadata;
}

// Export for both CommonJS (tests) and global scope (extension)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    createContextMenus,
    isContextMenuCreated,
    resetContextMenuState,
    isVideoProcessing,
    markVideoAsProcessing,
    markVideoAsComplete,
    getProcessingVideos,
    clearProcessingVideos,
    extractVideoIdFromContextMenu,
    getVideoTitle,
    getVideoMetadata
  };
} else {
  // For browser extension context
  window.ContextMenuManager = {
    createContextMenus,
    isContextMenuCreated,
    resetContextMenuState,
    isVideoProcessing,
    markVideoAsProcessing,
    markVideoAsComplete,
    getProcessingVideos,
    clearProcessingVideos,
    extractVideoIdFromContextMenu,
    getVideoTitle,
    getVideoMetadata
  };
}