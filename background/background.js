// Log when background script loads
console.log("[YT-Bookmarker] Background script loaded. Services available:", {
  BrowserOpenAIService: typeof BrowserOpenAIService,
  cleanTranscript: typeof cleanTranscript,
  generateTitleResponse: typeof generateTitleResponse,
  transformTranscriptToArticle: typeof transformTranscriptToArticle,
});

console.log("[YT-Bookmarker] Prompt globals loaded:", {
  TRANSCRIPT_CLEANER_PROMPTS: typeof window.TRANSCRIPT_CLEANER_PROMPTS,
  TITLE_RESPONSE_PROMPTS: typeof window.TITLE_RESPONSE_PROMPTS,
  INVESTIGATIVE_REPORTER_PROMPTS: typeof window.INVESTIGATIVE_REPORTER_PROMPTS,
  nunjucksPrecompiled: typeof window.nunjucksPrecompiled,
});

// A helper function to extract the YouTube video ID from a URL
function getVideoId(url) {
  if (!url) return null;

  // Handle YouTube Shorts URLs first
  const shortsMatch = url.match(/youtube\.com\/shorts\/([^"&?\/\s]{11})/);
  if (shortsMatch && shortsMatch[1]) {
    return shortsMatch[1];
  }

  // Regular YouTube video regex
  const youtubeRegex =
    /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
  const match = url.match(youtubeRegex);
  return match && match[1] ? match[1] : null;
}

// Video processing tracking is now handled by ContextMenuManager

// Helper function to check if API setup tab is already open
async function findExistingApiSetupTab() {
  try {
    const setupUrl = browser.runtime.getURL("ui/api-key-setup.html");
    const tabs = await browser.tabs.query({ url: setupUrl });
    return tabs.length > 0 ? tabs[0] : null;
  } catch (error) {
    console.error(
      "[YT-Bookmarker] Error checking for existing API setup tab:",
      error,
    );
    return null;
  }
}

// Helper function to open API setup tab with deduplication
async function openApiSetupTab() {
  try {
    // Check if setup tab is already open
    const existingTab = await findExistingApiSetupTab();
    if (existingTab) {
      console.log("[YT-Bookmarker] API setup tab already exists, focusing it");
      await browser.tabs.update(existingTab.id, { active: true });
      return existingTab;
    }

    // Create new setup tab
    console.log("[YT-Bookmarker] Creating new API setup tab");
    const newTab = await browser.tabs.create({
      url: browser.runtime.getURL("ui/api-key-setup.html"),
    });
    return newTab;
  } catch (error) {
    console.error("[YT-Bookmarker] Error opening API setup tab:", error);
    return null;
  }
}

// Check for API keys on startup
async function checkApiKeysOnStartup() {
  try {
    const result = await browser.storage.local.get([
      "apify_api_key",
      "openai_api_key",
      "deepseek_api_key",
      "api_keys_configured",
      "api_key_setup_skipped",
    ]);

    const hasApifyKey = !!result.apify_api_key;
    const hasOpenAIKey = !!result.openai_api_key;
    const hasDeepSeekKey = !!result.deepseek_api_key;
    const hasAllKeys = hasApifyKey && hasOpenAIKey;

    // If missing any API keys and user hasn't skipped setup, show setup page
    if (!hasAllKeys && !result.api_key_setup_skipped) {
      console.log("[YT-Bookmarker] Missing API keys, opening setup page");
      console.log(
        `[YT-Bookmarker] Has Apify: ${hasApifyKey}, Has OpenAI: ${hasOpenAIKey}, Has DeepSeek: ${hasDeepSeekKey}`,
      );
      await openApiSetupTab();
    } else if (hasAllKeys) {
      console.log(
        "[YT-Bookmarker] Core API keys found, full functionality available",
      );
    } else {
      console.log(
        "[YT-Bookmarker] API key setup was skipped, some features may be disabled",
      );
      if (!hasApifyKey)
        console.log(
          "[YT-Bookmarker] - Transcript extraction disabled (no Apify key)",
        );
      if (!hasOpenAIKey)
        console.log(
          "[YT-Bookmarker] - Article generation disabled (no OpenAI key)",
        );
      if (!hasDeepSeekKey)
        console.log(
          "[YT-Bookmarker] - DeepSeek features disabled (no DeepSeek key)",
        );
    }
  } catch (error) {
    console.error("[YT-Bookmarker] Error checking API keys on startup:", error);
  }
}

// Create context menus immediately when the background script loads
ContextMenuManager.createContextMenus();

// Check API keys on startup
checkApiKeysOnStartup();

// Debug service availability on startup
console.log("[YT-Bookmarker] Pipeline services loaded:", {
  BrowserOpenAIService: typeof BrowserOpenAIService,
  cleanTranscript: typeof cleanTranscript,
  generateTitleResponse: typeof generateTitleResponse,
  transformTranscriptToArticle: typeof transformTranscriptToArticle,
});

// Fired when the extension is first installed, or updated.
browser.runtime.onInstalled.addListener((details) => {
  ContextMenuManager.createContextMenus();

  // Only log install event - API key check already happens on startup
  if (details.reason === "install") {
    console.log("[YT-Bookmarker] Extension installed");
  }
});

// Fired when a context menu item is clicked
// Function to extract transcript using Apify actor
async function getYouTubeTranscript(videoId) {
  try {
    if (typeof getApifyTranscript !== "function") {
      throw new Error(
        "getApifyTranscript function not found - Apify module may not have loaded",
      );
    }

    // Use the Apify transcript service
    const transcript = await getApifyTranscript(videoId);
    return transcript;
  } catch (error) {
    console.error("[Background] Error in getYouTubeTranscript:", error);
    throw error;
  }
}

// Function to generate transcript HTML page
function generateTranscriptPage(transcript, videoTitle, videoId) {
  const html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transcript: ${videoTitle}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .header h1 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 24px;
        }
        .video-link {
            color: #1976d2;
            text-decoration: none;
            font-weight: 500;
        }
        .video-link:hover {
            text-decoration: underline;
        }
        .transcript {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            font-size: 16px;
            line-height: 1.8;
            color: #444;
        }
        .transcript p {
            margin-bottom: 1em;
        }
        .meta {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>${videoTitle}</h1>
        <div class="meta">Video ID: ${videoId}</div>
        <a href="https://www.youtube.com/watch?v=${videoId}" class="video-link" target="_blank">
            🎥 Watch on YouTube
        </a>
    </div>
    
    <div class="transcript">
        <h2>Transcript</h2>
        ${transcript
          .split(". ")
          .map((sentence) =>
            sentence.trim()
              ? `<p>${sentence.trim()}${
                  sentence.trim().endsWith(".") ? "" : "."
                }</p>`
              : "",
          )
          .join("")}
    </div>
</body>
</html>`;

  return html;
}

// Function to generate article from transcript using investigative reporter service
async function generateArticleFromTranscript(transcript) {
  try {
    // Check if the browser service is available
    if (typeof transformTranscriptToArticle !== "function") {
      throw new Error(
        "transformTranscriptToArticle function not available - browser service may not have loaded",
      );
    }

    // Use legacy mode with minimal parameters for backward compatibility
    const article = await transformTranscriptToArticle(
      "Legacy Article Generation", // videoTitle
      "Generated from transcript", // subtitle
      "Legacy analysis mode", // analysis
      transcript, // intelligence (using transcript as fallback)
      10, // duration
    );
    return article;
  } catch (error) {
    console.error("[YT-Bookmarker] Error generating article:", error);
    throw error;
  }
}

// 3-Stage Pipeline Functions

// Stage 1: Clean and reorganize transcript
async function processStage1_CleanTranscript(rawTranscript) {
  try {
    if (typeof cleanTranscript !== "function") {
      throw new Error(
        "cleanTranscript function not available - service may not have loaded",
      );
    }

    console.log(
      `[YT-Bookmarker] Stage 1: Cleaning and reorganizing transcript (length: ${rawTranscript.length})`,
    );
    const cleanedTranscript = await cleanTranscript(rawTranscript);
    console.log(
      `[YT-Bookmarker] Stage 1: Transcript cleaned and reorganized (length: ${cleanedTranscript.length})`,
    );

    return cleanedTranscript;
  } catch (error) {
    console.error("[YT-Bookmarker] Stage 1 error:", error);
    throw error;
  }
}

// Stage 2: Title response generation
async function processStage2_TitleResponse(videoTitle, cleanTranscript) {
  try {
    if (typeof generateTitleResponse !== "function") {
      throw new Error(
        "generateTitleResponse function not available - service may not have loaded",
      );
    }

    console.log(
      `[YT-Bookmarker] Stage 2: Generating title response for "${videoTitle}"`,
    );
    const titleResponse = await generateTitleResponse(
      videoTitle,
      cleanTranscript,
    );
    console.log(
      `[YT-Bookmarker] Stage 2: Title response generated: "${titleResponse}"`,
    );

    return titleResponse;
  } catch (error) {
    console.error("[YT-Bookmarker] Stage 2 error:", error);
    throw error;
  }
}

// Stage 3: Construct technical segment article
async function processStage3_Article(
  videoTitle,
  titleResponse,
  cleanedTranscript,
  duration = 10,
) {
  try {
    if (typeof transformTranscriptToArticle !== "function") {
      throw new Error(
        "transformTranscriptToArticle function not available - service may not have loaded",
      );
    }

    console.log(`[YT-Bookmarker] Stage 3: Constructing technical segment`);
    const article = await transformTranscriptToArticle(
      videoTitle,
      titleResponse,
      cleanedTranscript,
      duration,
    );
    console.log(
      `[YT-Bookmarker] Stage 3: Technical segment constructed (length: ${article.length})`,
    );

    return article;
  } catch (error) {
    console.error("[YT-Bookmarker] Stage 3 error:", error);
    throw error;
  }
}

// Function to run 3-stage pipeline in background after video is bookmarked
async function fetchTranscriptInBackground(videoId, videoTitle) {
  console.log(
    `[YT-Bookmarker] Starting 3-stage pipeline for video ${videoId}: "${videoTitle}"`,
  );

  try {
    // Step 1: Fetch raw transcript from Apify
    console.log(`[YT-Bookmarker] Fetching raw transcript for video ${videoId}`);
    const rawTranscript = await getYouTubeTranscript(videoId);
    console.log(
      `[YT-Bookmarker] Raw transcript fetched (length: ${rawTranscript.length})`,
    );

    // Step 2: Run 3-stage simplified pipeline
    let cleanTranscript = null;
    let titleResponse = null;
    let article = null;

    // Stage 1: Clean and reorganize transcript
    try {
      cleanTranscript = await processStage1_CleanTranscript(rawTranscript);
    } catch (stage1Error) {
      console.error(
        `[YT-Bookmarker] Stage 1 failed for video ${videoId}:`,
        stage1Error,
      );
      // Continue with raw transcript if cleaning fails
      cleanTranscript = rawTranscript;
    }

    // Stage 2: Generate title response
    try {
      titleResponse = await processStage2_TitleResponse(
        videoTitle,
        cleanTranscript,
      );
    } catch (stage2Error) {
      console.error(
        `[YT-Bookmarker] Stage 2 failed for video ${videoId}:`,
        stage2Error,
      );
      // Continue without title response if generation fails
      titleResponse = `Error generating title response: ${stage2Error.message}`;
    }

    // Stage 3: Generate article (simplified - investigative reporter needs to be updated for new inputs)
    try {
      const duration = Math.max(10, Math.round(rawTranscript.length / 100)); // Estimate duration from transcript length
      // Use updated function signature with correct parameters
      article = await transformTranscriptToArticle(
        videoTitle, // title
        titleResponse, // title_response
        cleanTranscript, // cleaned_transcript
        duration,
      );
    } catch (stage3Error) {
      console.error(
        `[YT-Bookmarker] Stage 3 failed for video ${videoId}:`,
        stage3Error,
      );
      // Continue without article if generation fails
    }

    // Update storage with all pipeline results
    const result = await browser.storage.local.get("videos");
    const videos = result.videos || [];
    const videoIndex = videos.findIndex((v) => v.id === videoId);

    if (videoIndex !== -1) {
      videos[videoIndex].transcript = rawTranscript; // Store raw transcript
      videos[videoIndex].cleanTranscript = cleanTranscript; // Store cleaned/reorganized transcript
      videos[videoIndex].titleResponse = titleResponse; // Store title response
      videos[videoIndex].article = article; // Store constructed article
      videos[videoIndex].transcriptStatus = "ready";
      // Remove old audio fields
      delete videos[videoIndex].audioData;
      delete videos[videoIndex].hasAudio;

      await browser.storage.local.set({ videos: videos });
      console.log(
        `[YT-Bookmarker] 3-stage pipeline completed for video ${videoId}`,
      );
    }
  } catch (error) {
    console.error(
      `[YT-Bookmarker] Pipeline failed for video ${videoId}:`,
      error,
    );

    // Update storage with error status
    try {
      const result = await browser.storage.local.get("videos");
      const videos = result.videos || [];
      const videoIndex = videos.findIndex((v) => v.id === videoId);

      if (videoIndex !== -1) {
        videos[videoIndex].transcript = null;
        videos[videoIndex].cleanTranscript = null;
        videos[videoIndex].titleResponse = null;
        videos[videoIndex].article = null;
        videos[videoIndex].robustOpenaiAnalysis = null;
        videos[videoIndex].openaiAnalysis = null;
        videos[videoIndex].transcriptStatus = "error";
        videos[videoIndex].transcriptError =
          error.message || "Failed to process video";
        // Remove old audio fields
        delete videos[videoIndex].audioData;
        delete videos[videoIndex].hasAudio;

        await browser.storage.local.set({ videos: videos });
      }
    } catch (storageError) {
      console.error(
        "Failed to update storage after pipeline error",
        storageError.message,
      );
    }
  }
}

// Background script wake-up and service initialization function
function ensureServicesReady() {
  const services = {
    BrowserOpenAIService: typeof BrowserOpenAIService,
    OpenAIVideoAnalyzer: typeof OpenAIVideoAnalyzer,
    RobustOpenAIService: typeof RobustOpenAIService,
    RobustVideoAnalyzer: typeof RobustVideoAnalyzer,
    getApifyTranscript: typeof getApifyTranscript,
    cleanTranscript: typeof cleanTranscript,
    generateTitleResponse: typeof generateTitleResponse,
    transformTranscriptToArticle: typeof transformTranscriptToArticle,
  };

  console.log("[YT-Bookmarker] Pipeline service availability check:", services);

  const missingServices = Object.entries(services)
    .filter(([name, type]) => type === "undefined")
    .map(([name]) => name);

  if (missingServices.length > 0) {
    console.error(
      "[YT-Bookmarker] Missing pipeline services on background script wake-up:",
      missingServices,
    );
    return false;
  }

  return true;
}

// Enhanced message listener with comprehensive debugging for connection issues
browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
  const messageId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  console.log(`[YT-Bookmarker] Message received [${messageId}]:`, {
    action: message.action,
    videoId: message.videoId,
    senderTabId: sender.tab?.id,
    timestamp: new Date().toISOString(),
  });

  // Handle ping messages for wake-up
  if (message.action === "ping") {
    console.log(
      `[YT-Bookmarker] Ping received [${messageId}], background script is awake`,
    );
    sendResponse({ success: true, pong: true });
    return false;
  }

  // Critical: Ensure services are available when background script wakes up
  const servicesReady = ensureServicesReady();
  if (message.action === "openInTab") {
    // Open the bookmarks page in a new tab
    browser.tabs
      .create({
        url: browser.runtime.getURL("ui/bookmarks.html"),
      })
      .then(() => {
        sendResponse({ success: true });
      })
      .catch((error) => {
        console.error(
          "[Background] Error opening bookmarks page in tab:",
          error,
        );
        sendResponse({ success: false, error: error.message });
      });
    return true; // Indicates we will send a response asynchronously
  }

  if (message.action === "openApiSetup") {
    // Open API setup tab with deduplication
    openApiSetupTab()
      .then((tab) => {
        sendResponse({ success: true, tab: tab });
      })
      .catch((error) => {
        console.error("[Background] Error opening API setup tab:", error);
        sendResponse({ success: false, error: error.message });
      });
    return true; // Indicates we will send a response asynchronously
  }

  if (message.action === "getTranscript") {
    (async () => {
      try {
        const transcript = await getYouTubeTranscript(message.videoId);

        // Store transcript for future use
        const result = await browser.storage.local.get("videos");
        const videos = result.videos || [];
        const videoIndex = videos.findIndex((v) => v.id === message.videoId);

        if (videoIndex !== -1) {
          videos[videoIndex].transcript = transcript;

          // Generate article from transcript
          let article = null;
          try {
            article = await generateArticleFromTranscript(transcript);
            console.log(
              `[YT-Bookmarker] Article generated for video ${message.videoId}`,
            );
          } catch (articleError) {
            console.error(
              `[YT-Bookmarker] Failed to generate article for video ${message.videoId}:`,
              articleError,
            );
          }

          videos[videoIndex].article = article;
          videos[videoIndex].transcriptStatus = "ready";
          await browser.storage.local.set({ videos: videos });
        }

        sendResponse({ success: true });
      } catch (error) {
        console.error("[Background] Error getting transcript:", error);

        // Update storage with error status
        const result = await browser.storage.local.get("videos");
        const videos = result.videos || [];
        const videoIndex = videos.findIndex((v) => v.id === message.videoId);

        if (videoIndex !== -1) {
          videos[videoIndex].transcriptStatus = "error";
          videos[videoIndex].transcriptError =
            error.message || "Failed to fetch transcript";
          await browser.storage.local.set({ videos: videos });
        }

        sendResponse({ success: false, error: error.message });
      }
    })();

    return true; // Indicates we will send a response asynchronously
  }

  if (message.action === "showTranscript") {
    (async () => {
      try {
        // Get video from storage
        const result = await browser.storage.local.get("videos");
        const videos = result.videos || [];
        const video = videos.find((v) => v.id === message.videoId);

        if (!video) {
          throw new Error("Video not found in storage");
        }

        if (video.transcriptStatus === "ready" && video.transcript) {
          // Transcript is ready, show it immediately
          const transcriptPage = generateTranscriptPage(
            video.transcript,
            video.title,
            video.id,
          );

          const blob = new Blob([transcriptPage], { type: "text/html" });
          const blobUrl = URL.createObjectURL(blob);
          await browser.tabs.create({ url: blobUrl });

          sendResponse({ success: true, status: "ready" });
        } else if (video.transcriptStatus === "fetching") {
          // Still fetching, let user know
          sendResponse({
            success: false,
            status: "fetching",
            message:
              "Transcript is still being fetched. Please wait and try again.",
          });
        } else if (video.transcriptStatus === "error") {
          // Error occurred, show error page
          const errorMessage =
            video.transcriptError ||
            "Unknown error occurred while fetching transcript";
          const errorPage = generateTranscriptPage(
            `Sorry, no transcript is available for this video. 
            
            ERROR: ${errorMessage}
            
            This could be because:
            • The video doesn't have captions enabled
            • The video has auto-generated captions that aren't accessible via API
            • The video is private or restricted
            • Technical issues with YouTube's transcript service
            
            You can try watching the video directly and enabling captions manually.`,
            video.title,
            video.id,
          );

          const blob = new Blob([errorPage], { type: "text/html" });
          const blobUrl = URL.createObjectURL(blob);
          await browser.tabs.create({ url: blobUrl });

          sendResponse({
            success: false,
            status: "error",
            message: errorMessage,
          });
        } else {
          // Unknown status
          sendResponse({
            success: false,
            status: "unknown",
            message: "Unknown transcript status",
          });
        }
      } catch (error) {
        sendResponse({
          success: false,
          status: "error",
          message: error.message,
        });
      }
    })();

    return true; // Indicates we will send a response asynchronously
  }
});

browser.contextMenus.onClicked.addListener(async (info, tab) => {
  const clickTime = Date.now();
  console.log(`[YT-Bookmarker] Context menu clicked at ${clickTime}`);

  try {
    // Make sure we're on YouTube
    if (!tab.url || !tab.url.includes("youtube.com")) {
      console.log("[YT-Bookmarker] Not on YouTube, ignoring click");
      return;
    }

    // Use ContextMenuManager to extract video ID and URL
    const { videoId, videoUrl } =
      await ContextMenuManager.extractVideoIdFromContextMenu(info, tab);

    if (!videoId) {
      console.log("[YT-Bookmarker] No video ID found, exiting");
      return;
    }

    console.log(`[YT-Bookmarker] Found video ID: ${videoId}`);

    // Check if this video is already being processed using ContextMenuManager
    if (ContextMenuManager.isVideoProcessing(videoId)) {
      console.log(
        `[YT-Bookmarker] Video ${videoId} is already being processed`,
      );
      return;
    }

    // Mark as processing using ContextMenuManager
    ContextMenuManager.markVideoAsProcessing(videoId);

    try {
      const currentVideos =
        (await browser.storage.local.get("videos")).videos || [];

      if (currentVideos.some((video) => video.id === videoId)) {
        console.log(`[YT-Bookmarker] Video ${videoId} already bookmarked`);
        ContextMenuManager.markVideoAsComplete(videoId);
        return;
      }

      // Use ContextMenuManager to get video title and metadata
      const title = await ContextMenuManager.getVideoTitle(tab, videoId);
      const metadata = await ContextMenuManager.getVideoMetadata(tab, videoId);

      const newVideo = {
        id: videoId,
        url: `https://www.youtube.com/watch?v=${videoId}`,
        title: title,
        thumbnailUrl: `https://i.ytimg.com/vi/${videoId}/mqdefault.jpg`,
        dateAdded: Date.now(),
        transcript: null, // Raw transcript from Apify
        cleanTranscript: null, // Cleaned transcript from Stage 1
        titleResponse: null, // Title response from Stage 2
        article: null, // Article from Stage 3
        transcriptStatus: "fetching",
        duration: metadata.duration,
        channelName: metadata.channelName,
        channelAvatar: metadata.channelAvatar,
        viewCount: metadata.viewCount,
        uploadTime: metadata.uploadTime,
      };

      const updatedVideos = [newVideo, ...currentVideos];
      await browser.storage.local.set({ videos: updatedVideos });

      console.log(`[YT-Bookmarker] Video ${videoId} saved successfully`);
      const saveTime = Date.now();
      console.log(
        `[YT-Bookmarker] Total time from click to save: ${
          saveTime - clickTime
        }ms`,
      );

      // Start fetching transcript immediately in background
      console.log(
        `[YT-Bookmarker] Starting background transcript fetch for ${videoId}`,
      );
      fetchTranscriptInBackground(videoId, title);

      // Remove from processing after a delay to allow duplicate detection
      setTimeout(() => {
        ContextMenuManager.markVideoAsComplete(videoId);
      }, 1000);
    } catch (error) {
      console.error(
        "[YT-Bookmarker] Error saving video in context menu handler",
        error,
      );
      ContextMenuManager.markVideoAsComplete(videoId);
    }
  } catch (error) {
    console.error(
      "[YT-Bookmarker] Unhandled error in context menu handler",
      error,
    );
    // Try to extract videoId from error context for cleanup
    if (videoId) {
      ContextMenuManager.markVideoAsComplete(videoId);
    }
  }

  const endTime = Date.now();
  console.log(
    `[YT-Bookmarker] Context menu handler completed in ${
      endTime - clickTime
    }ms`,
  );
});
