// OpenAI Video Analysis Service with Tool Calling and Structured Output

class OpenAIVideoAnalyzer {
  constructor(apiKey, options = {}) {
    // Detect environment and use appropriate service
    if (typeof window !== 'undefined' || typeof browser !== 'undefined') {
      // Browser environment - use BrowserOpenAIService
      this.openaiService = new BrowserOpenAIService({
        apiKey: apiKey,
        defaultModel: options.model || "gpt-4o-mini"
      });
    } else {
      // Node.js environment - use full OpenAIService
      const OpenAIService = require('./openai-completion-service.js');
      this.openaiService = new OpenAIService({
        apiKey: apiKey,
        defaultModel: options.model || "gpt-4o-mini"
      });
    }
  }

  // Tool definitions for video analysis
  getVideoAnalysisTools() {
    return [
      {
        type: "function",
        function: {
          name: "extract_video_metadata",
          description: "Extract structured metadata from video title and transcript",
          parameters: {
            type: "object",
            properties: {
              main_topic: {
                type: "string",
                description: "The primary topic or subject of the video"
              },
              key_themes: {
                type: "array",
                items: { type: "string" },
                description: "List of key themes discussed in the video"
              },
              target_audience: {
                type: "string",
                description: "The intended audience for this video"
              },
              content_type: {
                type: "string",
                enum: ["tutorial", "review", "news", "entertainment", "educational", "vlog", "other"],
                description: "Type of content"
              },
              complexity_level: {
                type: "string",
                enum: ["beginner", "intermediate", "advanced"],
                description: "Technical complexity level"
              }
            },
            required: ["main_topic", "key_themes", "content_type"]
          }
        }
      },
      {
        type: "function",
        function: {
          name: "generate_video_summary",
          description: "Generate a structured summary of the video content",
          parameters: {
            type: "object",
            properties: {
              title: {
                type: "string",
                description: "A concise, descriptive title for the summary"
              },
              key_points: {
                type: "array",
                items: { type: "string" },
                description: "Main points covered in the video (3-5 points)"
              },
              detailed_summary: {
                type: "string",
                description: "A comprehensive summary of the video content"
              },
              takeaways: {
                type: "array",
                items: { type: "string" },
                description: "Key takeaways or actionable insights"
              },
              duration_estimate: {
                type: "string",
                description: "Estimated reading time for the summary"
              }
            },
            required: ["title", "key_points", "detailed_summary"]
          }
        }
      },
      {
        type: "function",
        function: {
          name: "analyze_transcript_quality",
          description: "Analyze the quality and completeness of a video transcript",
          parameters: {
            type: "object",
            properties: {
              quality_score: {
                type: "number",
                minimum: 1,
                maximum: 10,
                description: "Quality score from 1-10"
              },
              completeness: {
                type: "string",
                enum: ["complete", "partial", "incomplete"],
                description: "How complete the transcript appears to be"
              },
              issues: {
                type: "array",
                items: { type: "string" },
                description: "Any quality issues found in the transcript"
              },
              recommendations: {
                type: "array",
                items: { type: "string" },
                description: "Recommendations for improving transcript quality"
              }
            },
            required: ["quality_score", "completeness"]
          }
        }
      }
    ];
  }

  // Extract video metadata using tool calling
  async extractVideoMetadata(title, transcript) {
    try {
      const messages = [
        {
          role: "system",
          content: "You are a video content analyzer. Use the extract_video_metadata tool to analyze the provided video title and transcript."
        },
        {
          role: "user",
          content: `Analyze this video:
Title: ${title}
Transcript: ${transcript.substring(0, 2000)}...`
        }
      ];

      const tools = this.getVideoAnalysisTools().filter(tool => 
        tool.function.name === "extract_video_metadata"
      );

      const response = await this.openaiService.callWithTools(messages, tools, "required");

      if (response.hasToolCalls()) {
        const toolCall = response.getFirstToolCall();
        return JSON.parse(toolCall.function.arguments);
      }

      throw new Error("No tool call response received");
    } catch (error) {
      console.error("[OpenAI Video Analyzer] Error extracting metadata:", error);
      throw error;
    }
  }

  // Generate structured video summary using JSON output
  async generateStructuredSummary(title, transcript) {
    try {
      const systemPrompt = `You are a professional video summarizer. Create a structured JSON summary that includes:
- title: A clear, descriptive title for the summary
- key_points: Array of 3-5 main points covered
- detailed_summary: Comprehensive summary paragraph
- takeaways: Array of actionable insights
- duration_estimate: Estimated reading time

IMPORTANT: Return ONLY valid JSON without any markdown formatting, code blocks, or additional text. The response must be parseable by JSON.parse().`;

      const userPrompt = `Summarize this video:
Title: ${title}
Transcript: ${transcript}`;

      const response = await this.openaiService.withSystemJSON(systemPrompt, userPrompt, {
        max_tokens: 1000 // Increase token limit for complete JSON responses
      });
      return response.parseJSONContent();
    } catch (error) {
      console.error("[OpenAI Video Analyzer] Error generating summary:", error);
      throw error;
    }
  }

  // Analyze transcript quality using tool calling
  async analyzeTranscriptQuality(transcript) {
    try {
      const messages = [
        {
          role: "system",
          content: "You are a transcript quality analyzer. Use the analyze_transcript_quality tool to evaluate the provided transcript."
        },
        {
          role: "user",
          content: `Analyze the quality of this transcript:
${transcript}`
        }
      ];

      const tools = this.getVideoAnalysisTools().filter(tool => 
        tool.function.name === "analyze_transcript_quality"
      );

      const response = await this.openaiService.callWithTools(messages, tools, "required");

      if (response.hasToolCalls()) {
        const toolCall = response.getFirstToolCall();
        return JSON.parse(toolCall.function.arguments);
      }

      throw new Error("No tool call response received");
    } catch (error) {
      console.error("[OpenAI Video Analyzer] Error analyzing transcript quality:", error);
      throw error;
    }
  }

  // Generate article with structured sections using JSON output
  async generateStructuredArticle(title, transcript) {
    try {
      const systemPrompt = `You are a professional content writer. Transform the video transcript into a well-structured article with these JSON fields:
- article_title: Engaging title for the article
- introduction: Opening paragraph that hooks the reader
- main_sections: Array of objects with {heading: string, content: string}
- conclusion: Closing paragraph with key takeaways
- tags: Array of relevant tags/categories
- word_count: Estimated word count

IMPORTANT: Return ONLY valid JSON without any markdown formatting, code blocks, or additional text. The response must be parseable by JSON.parse().`;

      const userPrompt = `Create an article from this video:
Title: ${title}
Transcript: ${transcript}`;

      const response = await this.openaiService.withSystemJSON(systemPrompt, userPrompt, {
        max_tokens: 2000
      });
      
      return response.parseJSONContent();
    } catch (error) {
      console.error("[OpenAI Video Analyzer] Error generating article:", error);
      throw error;
    }
  }

  // Comprehensive video analysis combining multiple tools
  async analyzeVideo(videoData) {
    try {
      const { title, transcript } = videoData;
      
      if (!transcript) {
        throw new Error("Transcript is required for video analysis");
      }

      console.log(`[OpenAI Video Analyzer] Starting comprehensive analysis for: ${title}`);

      // Run analysis tasks in parallel for better performance
      const [metadata, summary, quality, article] = await Promise.allSettled([
        this.extractVideoMetadata(title, transcript),
        this.generateStructuredSummary(title, transcript),
        this.analyzeTranscriptQuality(transcript),
        this.generateStructuredArticle(title, transcript)
      ]);

      const result = {
        metadata: metadata.status === 'fulfilled' ? metadata.value : null,
        summary: summary.status === 'fulfilled' ? summary.value : null,
        quality: quality.status === 'fulfilled' ? quality.value : null,
        article: article.status === 'fulfilled' ? article.value : null,
        errors: []
      };

      // Collect any errors
      [metadata, summary, quality, article].forEach((promiseResult, index) => {
        if (promiseResult.status === 'rejected') {
          const taskNames = ['metadata', 'summary', 'quality', 'article'];
          result.errors.push({
            task: taskNames[index],
            error: promiseResult.reason.message
          });
        }
      });

      console.log(`[OpenAI Video Analyzer] Analysis completed for: ${title}`);
      return result;
    } catch (error) {
      console.error("[OpenAI Video Analyzer] Error in comprehensive analysis:", error);
      throw error;
    }
  }
}

// Export for both CommonJS (tests) and global scope (extension)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { OpenAIVideoAnalyzer };
} else {
  // Export for browser/extension environment
  if (typeof window !== 'undefined') {
    window.OpenAIVideoAnalyzer = OpenAIVideoAnalyzer;
  } else if (typeof globalThis !== 'undefined') {
    globalThis.OpenAIVideoAnalyzer = OpenAIVideoAnalyzer;
  }
}