// Robust OpenAI Service with proper Tool Calling and JSON Object support
// Environment-agnostic implementation for both Node.js and Browser

class RobustOpenAIService {
  constructor(config = {}) {
    this.config = {
      apiKey: config.apiKey || null,
      baseUrl: config.baseUrl || "https://api.openai.com/v1",
      defaultModel: config.defaultModel || "gpt-4o-mini",
      timeout: config.timeout || 60000,
    };

    if (!this.config.apiKey) {
      throw new Error("API key is required for OpenAI service");
    }
  }

  // Core chat completion method with full support for tools and json_object
  async chat(messages, options = {}) {
    const requestBody = {
      model: options.model || this.config.defaultModel,
      messages: this._validateAndFormatMessages(messages),
      max_tokens: options.max_tokens || 2000,
      temperature: options.temperature || 0.7,
      top_p: options.top_p || 1.0,
      frequency_penalty: options.frequency_penalty || 0,
      presence_penalty: options.presence_penalty || 0,
    };

    // Add tool calling support
    if (options.tools && Array.isArray(options.tools)) {
      requestBody.tools = options.tools;
      if (options.tool_choice) {
        requestBody.tool_choice = options.tool_choice;
      }
    }

    // Add JSON object response format
    if (options.response_format?.type === "json_object") {
      requestBody.response_format = { type: "json_object" };
    }

    try {
      const response = await this._makeRequest(requestBody);
      return new RobustOpenAIResponse(response);
    } catch (error) {
      throw new Error(`OpenAI API request failed: ${error.message}`);
    }
  }

  // Tool calling method with complete conversation flow
  async callWithTools(messages, tools, toolChoice = "auto", options = {}) {
    const toolOptions = {
      ...options,
      tools: tools,
      tool_choice: toolChoice,
    };

    const response = await this.chat(messages, toolOptions);
    
    if (response.hasToolCalls()) {
      return response; // Return response with tool calls for external handling
    } else {
      return response; // Return regular response if no tools were called
    }
  }

  // Execute tool call workflow and get final response
  async executeToolWorkflow(messages, tools, toolExecutor, options = {}) {
    // Initial request with tools
    let response = await this.callWithTools(messages, tools, "auto", options);
    let conversationMessages = [...messages];

    // Handle tool calls if present
    if (response.hasToolCalls()) {
      // Add assistant message with tool calls
      conversationMessages.push({
        role: "assistant",
        content: response.getContent(),
        tool_calls: response.getToolCalls()
      });

      // Execute each tool call
      for (const toolCall of response.getToolCalls()) {
        try {
          const toolResult = await toolExecutor(toolCall);
          conversationMessages.push({
            role: "tool",
            tool_call_id: toolCall.id,
            content: JSON.stringify(toolResult)
          });
        } catch (toolError) {
          conversationMessages.push({
            role: "tool",
            tool_call_id: toolCall.id,
            content: JSON.stringify({ error: toolError.message })
          });
        }
      }

      // Get final response after tool execution
      response = await this.chat(conversationMessages, { ...options, tools: undefined });
    }

    return response;
  }

  // JSON object response with proper schema in prompt
  async getJSONResponse(systemPrompt, userPrompt, jsonSchema, options = {}) {
    // Create enhanced prompts that include the JSON schema
    const enhancedSystemPrompt = `${systemPrompt}

You must respond with valid JSON that matches this exact schema:
${JSON.stringify(jsonSchema, null, 2)}

Respond ONLY with valid JSON. Do not include any markdown, explanations, or other text.`;

    const enhancedUserPrompt = `${userPrompt}

Please respond with JSON that matches the required schema.`;

    const messages = [
      { role: "system", content: enhancedSystemPrompt },
      { role: "user", content: enhancedUserPrompt }
    ];

    const jsonOptions = {
      ...options,
      response_format: { type: "json_object" }
    };

    const response = await this.chat(messages, jsonOptions);
    return response; // Return the response object so parseJSON() can be called
  }

  // Convenience method for simple JSON responses
  async simpleJSON(prompt, jsonSchema, options = {}) {
    const systemPrompt = "You are a helpful assistant that responds with valid JSON.";
    return await this.getJSONResponse(systemPrompt, prompt, jsonSchema, options);
  }

  // Validate and format messages
  _validateAndFormatMessages(messages) {
    if (!Array.isArray(messages) || messages.length === 0) {
      throw new Error("Messages must be a non-empty array");
    }

    return messages.map(msg => {
      if (!msg.role || !['system', 'user', 'assistant', 'tool'].includes(msg.role)) {
        throw new Error(`Invalid message role: ${msg.role}`);
      }

      const formattedMessage = {
        role: msg.role,
        content: msg.content || null
      };

      // Add tool-specific fields
      if (msg.tool_calls) {
        formattedMessage.tool_calls = msg.tool_calls;
      }
      if (msg.tool_call_id) {
        formattedMessage.tool_call_id = msg.tool_call_id;
      }

      return formattedMessage;
    });
  }

  // Make HTTP request to OpenAI API
  async _makeRequest(requestBody) {
    const url = `${this.config.baseUrl}/chat/completions`;

    // Environment-specific fetch implementation
    const fetchImpl = this._getFetchImplementation();

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

    try {
      const response = await fetchImpl(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.config.apiKey}`
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`;
        throw new Error(errorMessage);
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error(`Request timeout after ${this.config.timeout}ms`);
      }
      
      throw error;
    }
  }

  // Get appropriate fetch implementation for environment
  _getFetchImplementation() {
    if (typeof fetch !== 'undefined') {
      return fetch; // Browser or Node.js with fetch
    } else if (typeof require !== 'undefined') {
      // Node.js without fetch - would need to install node-fetch or use undici
      throw new Error("Fetch not available. Please ensure you're using Node.js 18+ or install node-fetch");
    } else {
      throw new Error("No fetch implementation available");
    }
  }
}

// Response wrapper with proper tool call and JSON handling
class RobustOpenAIResponse {
  constructor(responseData) {
    this.data = responseData;
    this.choices = responseData.choices || [];
    
    if (this.choices.length === 0) {
      throw new Error("No choices in OpenAI response");
    }
  }

  // Get response content
  getContent() {
    return this.choices[0].message?.content || "";
  }

  // Get usage information
  getUsage() {
    return this.data.usage;
  }

  // Get finish reason
  getFinishReason() {
    return this.choices[0].finish_reason;
  }

  // Get full message object
  getMessage() {
    return this.choices[0].message;
  }

  // Tool call detection
  hasToolCalls() {
    return this.getFinishReason() === "tool_calls" && 
           this.choices[0].message?.tool_calls?.length > 0;
  }

  // Get all tool calls
  getToolCalls() {
    if (!this.hasToolCalls()) {
      return [];
    }
    return this.choices[0].message.tool_calls;
  }

  // Get first tool call
  getFirstToolCall() {
    const toolCalls = this.getToolCalls();
    return toolCalls.length > 0 ? toolCalls[0] : null;
  }

  // Parse JSON response (should be clean from json_object format)
  parseJSON() {
    const content = this.getContent().trim();
    
    if (!content) {
      throw new Error("No content to parse as JSON");
    }

    try {
      return JSON.parse(content);
    } catch (error) {
      throw new Error(`Invalid JSON in response: ${error.message}. Content: ${content.substring(0, 200)}...`);
    }
  }

  // Validate that response matches expected schema
  validateJSONSchema(schema) {
    const jsonData = this.parseJSON();
    
    // Basic schema validation (you could use a library like ajv for more robust validation)
    if (schema.required) {
      for (const requiredField of schema.required) {
        if (!(requiredField in jsonData)) {
          throw new Error(`Missing required field: ${requiredField}`);
        }
      }
    }

    return jsonData;
  }
}

// Export for both environments
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { RobustOpenAIService, RobustOpenAIResponse };
} else {
  // Browser environment
  if (typeof window !== 'undefined') {
    window.RobustOpenAIService = RobustOpenAIService;
    window.RobustOpenAIResponse = RobustOpenAIResponse;
  } else if (typeof globalThis !== 'undefined') {
    globalThis.RobustOpenAIService = RobustOpenAIService;
    globalThis.RobustOpenAIResponse = RobustOpenAIResponse;
  }
}