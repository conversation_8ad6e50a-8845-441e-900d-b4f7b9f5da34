// Browser-compatible title response generator for Stage 2 of pipeline

async function generateTitleResponse(videoTitle, cleanTranscript) {
  if (
    !videoTitle ||
    typeof videoTitle !== "string" ||
    videoTitle.trim() === ""
  ) {
    throw new Error("Video title must be a non-empty string");
  }

  if (
    !cleanTranscript ||
    typeof cleanTranscript !== "string" ||
    cleanTranscript.trim() === ""
  ) {
    throw new Error("Clean transcript must be a non-empty string");
  }

  // DEBUG: Log the inputs being processed
  console.log("[YT-Bookmarker] DEBUG: Generating title response:");
  console.log("Video title:", videoTitle);
  console.log("Clean transcript length:", cleanTranscript.length);
  console.log("Clean transcript preview:", cleanTranscript.substring(0, 200));

  // Get API key from storage
  const result = await browser.storage.local.get("openai_api_key");
  const apiKey = result.openai_api_key;

  if (!apiKey) {
    throw new Error("OpenAI API key not configured");
  }

  const openai = new BrowserOpenAIService({ apiKey });

  // Get system prompt from auto-generated system prompts
  if (!window.TITLE_RESPONSE_PROMPTS) {
    console.error("[YT-Bookmarker] TITLE_RESPONSE_PROMPTS not available. Available globals:", Object.keys(window));
    throw new Error("TITLE_RESPONSE_PROMPTS not loaded - system-prompts.js may not have loaded properly");
  }
  
  if (!window.TITLE_RESPONSE_PROMPTS.SYSTEM_PROMPT) {
    console.error("[YT-Bookmarker] SYSTEM_PROMPT missing from TITLE_RESPONSE_PROMPTS:", window.TITLE_RESPONSE_PROMPTS);
    throw new Error("SYSTEM_PROMPT missing from TITLE_RESPONSE_PROMPTS");
  }
  
  const { SYSTEM_PROMPT } = window.TITLE_RESPONSE_PROMPTS;

  // Use the CSP-safe template service with precompiled templates
  if (typeof NunjucksService === 'undefined') {
    throw new Error("Template service not available - nunjucks-service.js may not have loaded");
  }

  const templateService = new NunjucksService();
  const userPrompt = templateService.renderTemplate('title-response.njk', { 
    title: videoTitle,
    transcript: cleanTranscript 
  });

  // DEBUG: Log the full prompt being sent
  console.log("[YT-Bookmarker] DEBUG: Title response prompt being sent:");
  console.log("System prompt length:", SYSTEM_PROMPT.length);
  console.log("User prompt length:", userPrompt.length);

  const response = await openai.withSystem(SYSTEM_PROMPT, userPrompt, {
    model: "gpt-4o-mini",
    temperature: 0.4, // Moderate temperature for focused response
    max_tokens: 200, // Fewer tokens for simple title response
  });

  const titleResponse = response.getContent().trim();

  // DEBUG: Log the AI response
  console.log("[YT-Bookmarker] DEBUG: Title response received:");
  console.log("Response length:", titleResponse.length);
  console.log("Response:", titleResponse);

  return titleResponse;
}

// Export for both CommonJS (tests) and global scope (extension)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    generateTitleResponse
  };
} else {
  // Export for browser/extension environment
  if (typeof window !== 'undefined') {
    window.generateTitleResponse = generateTitleResponse;
  } else if (typeof globalThis !== 'undefined') {
    globalThis.generateTitleResponse = generateTitleResponse;
  }
}