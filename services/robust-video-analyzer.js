// Robust Video Analyzer using the RobustOpenAIService
// Built on top of the robust OpenAI service with proper tool calling and JSON schemas

class RobustVideoAnalyzer {
  constructor(apiKey, options = {}) {
    // Environment detection for service instantiation
    if (typeof window !== 'undefined' || typeof browser !== 'undefined') {
      // Browser environment
      if (typeof RobustOpenAIService === 'undefined') {
        throw new Error('RobustOpenAIService not available in browser environment');
      }
      this.openaiService = new RobustOpenAIService({
        apiKey: apiKey,
        defaultModel: options.model || "gpt-4o-mini"
      });
    } else {
      // Node.js environment
      const { RobustOpenAIService } = require('./robust-openai-service.js');
      this.openaiService = new RobustOpenAIService({
        apiKey: apiKey,
        defaultModel: options.model || "gpt-4o-mini"
      });
    }
  }

  // Extract video metadata using tool calling
  async extractVideoMetadata(title, transcript) {
    const tools = [{
      type: "function",
      function: {
        name: "extract_video_metadata",
        description: "Extract structured metadata from video content",
        parameters: {
          type: "object",
          properties: {
            main_topic: {
              type: "string",
              description: "The primary topic or subject of the video"
            },
            key_themes: {
              type: "array",
              items: { type: "string" },
              description: "List of 3-5 key themes discussed in the video"
            },
            target_audience: {
              type: "string",
              description: "The intended audience for this video"
            },
            content_type: {
              type: "string",
              enum: ["tutorial", "review", "news", "entertainment", "educational", "vlog", "other"],
              description: "Type of content"
            },
            complexity_level: {
              type: "string",
              enum: ["beginner", "intermediate", "advanced"],
              description: "Technical complexity level"
            },
            estimated_duration: {
              type: "string",
              description: "Estimated viewing time"
            }
          },
          required: ["main_topic", "key_themes", "content_type", "complexity_level"]
        }
      }
    }];

    const messages = [
      {
        role: "system",
        content: "You are a video content analyzer. Use the extract_video_metadata tool to analyze the provided video title and transcript."
      },
      {
        role: "user",
        content: `Analyze this video:
Title: ${title}
Transcript: ${transcript.substring(0, 3000)}...`
      }
    ];

    const response = await this.openaiService.callWithTools(messages, tools, "required");

    if (response.hasToolCalls()) {
      const toolCall = response.getFirstToolCall();
      return JSON.parse(toolCall.function.arguments);
    }

    throw new Error("No metadata extracted from video analysis");
  }

  // Generate structured summary using JSON object format
  async generateStructuredSummary(title, transcript) {
    const summarySchema = {
      type: "object",
      properties: {
        title: {
          type: "string",
          description: "A clear, descriptive title for the summary"
        },
        key_points: {
          type: "array",
          items: { type: "string" },
          minItems: 3,
          maxItems: 5,
          description: "Main points covered in the video"
        },
        detailed_summary: {
          type: "string",
          description: "Comprehensive summary paragraph (150-300 words)"
        },
        takeaways: {
          type: "array",
          items: { type: "string" },
          description: "Key takeaways or actionable insights"
        },
        duration_estimate: {
          type: "string",
          description: "Estimated reading time for the summary"
        },
        relevance_score: {
          type: "number",
          minimum: 1,
          maximum: 10,
          description: "Relevance score of the content (1-10)"
        }
      },
      required: ["title", "key_points", "detailed_summary", "takeaways", "duration_estimate"]
    };

    const systemPrompt = "You are a professional video summarizer. Create comprehensive yet concise summaries.";
    const userPrompt = `Create a structured summary for this video:
Title: ${title}
Transcript: ${transcript}`;

    const response = await this.openaiService.getJSONResponse(
      systemPrompt,
      userPrompt,
      summarySchema,
      { max_tokens: 1500 }
    );

    return response.parseJSON();
  }

  // Analyze transcript quality using tool calling
  async analyzeTranscriptQuality(transcript) {
    const tools = [{
      type: "function",
      function: {
        name: "analyze_transcript_quality",
        description: "Analyze the quality and completeness of a video transcript",
        parameters: {
          type: "object",
          properties: {
            quality_score: {
              type: "number",
              minimum: 1,
              maximum: 10,
              description: "Quality score from 1-10 based on clarity, completeness, and accuracy"
            },
            completeness: {
              type: "string",
              enum: ["complete", "mostly_complete", "partial", "incomplete"],
              description: "How complete the transcript appears to be"
            },
            readability: {
              type: "string",
              enum: ["excellent", "good", "fair", "poor"],
              description: "How readable and well-formatted the transcript is"
            },
            issues: {
              type: "array",
              items: { type: "string" },
              description: "Any quality issues found in the transcript"
            },
            recommendations: {
              type: "array",
              items: { type: "string" },
              description: "Recommendations for improving transcript quality"
            },
            estimated_accuracy: {
              type: "number",
              minimum: 0,
              maximum: 100,
              description: "Estimated accuracy percentage of the transcript"
            }
          },
          required: ["quality_score", "completeness", "readability", "estimated_accuracy"]
        }
      }
    }];

    const messages = [
      {
        role: "system",
        content: "You are a transcript quality analyzer. Use the analyze_transcript_quality tool to evaluate the provided transcript."
      },
      {
        role: "user",
        content: `Analyze the quality of this transcript:
${transcript}`
      }
    ];

    const response = await this.openaiService.callWithTools(messages, tools, "required");

    if (response.hasToolCalls()) {
      const toolCall = response.getFirstToolCall();
      return JSON.parse(toolCall.function.arguments);
    }

    throw new Error("No quality analysis generated for transcript");
  }

  // Generate structured article using JSON object format
  async generateStructuredArticle(title, transcript) {
    const articleSchema = {
      type: "object",
      properties: {
        article_title: {
          type: "string",
          description: "Engaging title for the article"
        },
        subtitle: {
          type: "string",
          description: "Compelling subtitle that complements the main title"
        },
        introduction: {
          type: "string",
          description: "Opening paragraph that hooks the reader (100-150 words)"
        },
        main_sections: {
          type: "array",
          items: {
            type: "object",
            properties: {
              heading: { type: "string" },
              content: { type: "string" }
            },
            required: ["heading", "content"]
          },
          description: "Array of main content sections with headings and content"
        },
        conclusion: {
          type: "string",
          description: "Closing paragraph with key takeaways (100-150 words)"
        },
        tags: {
          type: "array",
          items: { type: "string" },
          description: "Relevant tags/categories for the article"
        },
        reading_time: {
          type: "string",
          description: "Estimated reading time"
        },
        word_count: {
          type: "number",
          description: "Estimated word count of the article"
        },
        seo_keywords: {
          type: "array",
          items: { type: "string" },
          description: "SEO-friendly keywords for the article"
        }
      },
      required: ["article_title", "introduction", "main_sections", "conclusion", "tags", "reading_time"]
    };

    const systemPrompt = "You are a professional content writer. Transform video transcripts into engaging, well-structured articles.";
    const userPrompt = `Create a comprehensive article from this video:
Title: ${title}
Transcript: ${transcript}`;

    const response = await this.openaiService.getJSONResponse(
      systemPrompt,
      userPrompt,
      articleSchema,
      { max_tokens: 3000 }
    );

    return response.parseJSON();
  }

  // Generate content recommendations using JSON object format
  async generateContentRecommendations(title, transcript, metadata) {
    const recommendationsSchema = {
      type: "object",
      properties: {
        related_topics: {
          type: "array",
          items: { type: "string" },
          description: "Topics related to this video content"
        },
        follow_up_questions: {
          type: "array",
          items: { type: "string" },
          description: "Questions viewers might have after watching"
        },
        recommended_actions: {
          type: "array",
          items: { type: "string" },
          description: "Actions viewers could take based on the content"
        },
        content_gaps: {
          type: "array",
          items: { type: "string" },
          description: "Areas that could be expanded or clarified"
        },
        audience_insights: {
          type: "object",
          properties: {
            primary_audience: { type: "string" },
            secondary_audience: { type: "string" },
            engagement_level: { 
              type: "string",
              enum: ["low", "medium", "high"]
            }
          },
          required: ["primary_audience", "engagement_level"]
        }
      },
      required: ["related_topics", "follow_up_questions", "recommended_actions", "audience_insights"]
    };

    const systemPrompt = "You are a content strategy expert. Analyze video content and provide strategic recommendations.";
    const userPrompt = `Analyze this video content and provide recommendations:
Title: ${title}
Content Type: ${metadata?.content_type || 'unknown'}
Target Audience: ${metadata?.target_audience || 'general'}
Transcript Summary: ${transcript.substring(0, 1000)}...`;

    const response = await this.openaiService.getJSONResponse(
      systemPrompt,
      userPrompt,
      recommendationsSchema,
      { max_tokens: 1500 }
    );

    return response.parseJSON();
  }

  // Comprehensive video analysis with all features
  async analyzeVideo(videoData) {
    const { title, transcript } = videoData;

    if (!transcript || transcript.trim().length === 0) {
      throw new Error("Transcript is required for video analysis");
    }

    console.log(`[Robust Video Analyzer] Starting comprehensive analysis for: ${title}`);

    try {
      // Run all analysis tasks in parallel for better performance
      const [metadataResult, summaryResult, qualityResult, articleResult] = await Promise.allSettled([
        this.extractVideoMetadata(title, transcript),
        this.generateStructuredSummary(title, transcript),
        this.analyzeTranscriptQuality(transcript),
        this.generateStructuredArticle(title, transcript)
      ]);

      // Extract results and track errors
      const results = {
        metadata: metadataResult.status === 'fulfilled' ? metadataResult.value : null,
        summary: summaryResult.status === 'fulfilled' ? summaryResult.value : null,
        quality: qualityResult.status === 'fulfilled' ? qualityResult.value : null,
        article: articleResult.status === 'fulfilled' ? articleResult.value : null,
        errors: []
      };

      // Collect any errors
      const taskNames = ['metadata', 'summary', 'quality', 'article'];
      [metadataResult, summaryResult, qualityResult, articleResult].forEach((result, index) => {
        if (result.status === 'rejected') {
          results.errors.push({
            task: taskNames[index],
            error: result.reason.message
          });
        }
      });

      // Generate recommendations if we have metadata
      if (results.metadata) {
        try {
          results.recommendations = await this.generateContentRecommendations(
            title, 
            transcript, 
            results.metadata
          );
        } catch (error) {
          results.errors.push({
            task: 'recommendations',
            error: error.message
          });
        }
      }

      console.log(`[Robust Video Analyzer] Analysis completed for: ${title} (${results.errors.length} errors)`);
      return results;

    } catch (error) {
      console.error(`[Robust Video Analyzer] Critical error analyzing video: ${error.message}`);
      throw error;
    }
  }

  // Quick analysis for basic metadata only
  async quickAnalyze(title, transcript) {
    try {
      const [metadata, summary] = await Promise.allSettled([
        this.extractVideoMetadata(title, transcript),
        this.generateStructuredSummary(title, transcript)
      ]);

      return {
        metadata: metadata.status === 'fulfilled' ? metadata.value : null,
        summary: summary.status === 'fulfilled' ? summary.value : null,
        success: metadata.status === 'fulfilled' || summary.status === 'fulfilled'
      };
    } catch (error) {
      console.error(`[Robust Video Analyzer] Quick analysis failed: ${error.message}`);
      return { success: false, error: error.message };
    }
  }
}

// Export for both environments
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { RobustVideoAnalyzer };
} else {
  // Browser environment
  if (typeof window !== 'undefined') {
    window.RobustVideoAnalyzer = RobustVideoAnalyzer;
  } else if (typeof globalThis !== 'undefined') {
    globalThis.RobustVideoAnalyzer = RobustVideoAnalyzer;
  }
}