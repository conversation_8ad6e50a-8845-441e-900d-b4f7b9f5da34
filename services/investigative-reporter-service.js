// Browser-compatible Slashdot article constructor service

// Import prompts from shared templates
// Note: prompts/investigative-reporter-prompts.js must be loaded before this script in manifest.json

async function transformTranscriptToArticle(videoTitle, titleResponse, cleanedTranscript, duration = 10) {
  if (!videoTitle || typeof videoTitle !== "string" || videoTitle.trim() === "") {
    throw new Error("Video title must be a non-empty string");
  }
  
  if (!titleResponse || typeof titleResponse !== "string" || titleResponse.trim() === "") {
    throw new Error("Title response must be a non-empty string");
  }
  
  if (!cleanedTranscript || typeof cleanedTranscript !== "string" || cleanedTranscript.trim() === "") {
    throw new Error("Cleaned transcript must be a non-empty string");
  }

  // DEBUG: Log the inputs being processed
  console.log("[YT-Bookmarker] DEBUG: Constructing technical segment:");
  console.log("Video title:", videoTitle);
  console.log("Title response:", titleResponse);
  console.log("Cleaned transcript length:", cleanedTranscript.length);
  console.log("Duration:", duration, "minutes");

  // Get API key from storage
  const result = await browser.storage.local.get("openai_api_key");
  const apiKey = result.openai_api_key;

  if (!apiKey) {
    throw new Error("OpenAI API key not configured");
  }

  const openai = new BrowserOpenAIService({ apiKey });

  // Get system prompt from auto-generated system prompts
  if (!window.INVESTIGATIVE_REPORTER_PROMPTS) {
    console.error("[YT-Bookmarker] INVESTIGATIVE_REPORTER_PROMPTS not available. Available globals:", Object.keys(window));
    throw new Error("INVESTIGATIVE_REPORTER_PROMPTS not loaded - system-prompts.js may not have loaded properly");
  }
  
  if (!window.INVESTIGATIVE_REPORTER_PROMPTS.SYSTEM_PROMPT) {
    console.error("[YT-Bookmarker] SYSTEM_PROMPT missing from INVESTIGATIVE_REPORTER_PROMPTS:", window.INVESTIGATIVE_REPORTER_PROMPTS);
    throw new Error("SYSTEM_PROMPT missing from INVESTIGATIVE_REPORTER_PROMPTS");
  }
  
  const { SYSTEM_PROMPT } = window.INVESTIGATIVE_REPORTER_PROMPTS;

  // Use the CSP-safe template service with precompiled templates
  if (typeof NunjucksService === 'undefined') {
    throw new Error("Template service not available - nunjucks-service.js may not have loaded");
  }

  const templateService = new NunjucksService();
  const userPrompt = templateService.renderTemplate('investigative-reporter.njk', { 
    title: videoTitle,
    duration: duration,
    title_response: titleResponse,
    cleaned_transcript: cleanedTranscript
  });

  // DEBUG: Log the full prompt being sent
  console.log("[YT-Bookmarker] DEBUG: Technical segment prompt being sent:");
  console.log("System prompt length:", SYSTEM_PROMPT.length);
  console.log("User prompt length:", userPrompt.length);
  console.log("User prompt preview:", userPrompt.substring(0, 500) + "...");

  const response = await openai.withSystem(SYSTEM_PROMPT, userPrompt, {
    model: "gpt-4o-mini",
    temperature: 0.6, // Moderate temperature for technical precision
    max_tokens: Math.max(400, Math.min(800, duration * 30)), // Scale tokens but cap for density
  });

  const technicalSegment = response.getContent();

  // DEBUG: Log the AI response
  console.log("[YT-Bookmarker] DEBUG: Technical segment constructed:");
  console.log("Segment length:", technicalSegment.length);
  console.log("Segment preview:", technicalSegment.substring(0, 300));

  return technicalSegment;
}

// Export for both CommonJS (tests) and global scope (extension)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    transformTranscriptToArticle
  };
} else {
  // Export for browser/extension environment
  if (typeof window !== 'undefined') {
    window.transformTranscriptToArticle = transformTranscriptToArticle;
  } else if (typeof globalThis !== 'undefined') {
    globalThis.transformTranscriptToArticle = transformTranscriptToArticle;
  }
}
