// Browser-compatible OpenAI service for Firefox extension

class BrowserOpenAIService {
  constructor(config = {}) {
    this.config = {
      apiKey: config.apiKey || null,
      baseUrl: config.baseUrl || "https://api.openai.com/v1",
      defaultModel: config.defaultModel || "gpt-4.1",
      timeout: config.timeout || 60000,
    };
  }

  async withSystem(systemPrompt, userPrompt, options = {}) {
    const messages = [
      { role: "system", content: systemPrompt },
      { role: "user", content: userPrompt },
    ];

    const response = await this.chat(messages, options);
    return response;
  }

  async callWithTools(messages, tools, toolChoice = "auto", options = {}) {
    const toolOptions = {
      ...options,
      tools: tools,
      tool_choice: toolChoice,
    };

    return await this.chat(messages, toolOptions);
  }

  async getJSONResponse(messages, options = {}) {
    const jsonOptions = {
      ...options,
      response_format: { type: "json_object" },
    };

    return await this.chat(messages, jsonOptions);
  }

  async withSystemJSON(systemPrompt, userPrompt, options = {}) {
    const messages = [
      { role: "system", content: systemPrompt },
      { role: "user", content: userPrompt },
    ];

    return await this.getJSONResponse(messages, options);
  }

  async chat(messages, options = {}) {
    return await this._makeRequestWithRetry(messages, options);
  }

  async _makeRequestWithRetry(messages, options = {}, maxRetries = 3) {
    if (!this.config.apiKey) {
      throw new Error("API key is required");
    }

    const requestBody = {
      model: options.model || this.config.defaultModel,
      messages: messages,
      max_tokens: options.max_tokens || 2000,
      temperature: options.temperature || 0.7,
      top_p: options.top_p || 1,
      frequency_penalty: options.frequency_penalty || 0,
      presence_penalty: options.presence_penalty || 0,
    };

    // Add tool calling support
    if (options.tools) {
      requestBody.tools = options.tools;
      if (options.tool_choice) {
        requestBody.tool_choice = options.tool_choice;
      }
    }

    // Add structured output support
    if (options.response_format) {
      requestBody.response_format = options.response_format;
    }

    const url = `${this.config.baseUrl}/chat/completions`;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

        const response = await fetch(url, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${this.config.apiKey}`,
          },
          body: JSON.stringify(requestBody),
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          const errorMessage =
            errorData.error?.message ||
            `HTTP ${response.status}: ${response.statusText}`;

          // Handle rate limiting with exponential backoff
          if (response.status === 429 && attempt < maxRetries) {
            const delay = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s
            console.warn(`[OpenAI Service] Rate limited, retrying in ${delay}ms...`);
            await this._sleep(delay);
            continue;
          }

          // Handle server errors with retry
          if (response.status >= 500 && attempt < maxRetries) {
            const delay = Math.pow(2, attempt) * 500; // 1s, 2s, 4s
            console.warn(`[OpenAI Service] Server error, retrying in ${delay}ms...`);
            await this._sleep(delay);
            continue;
          }

          throw new Error(errorMessage);
        }

        const responseData = await response.json();
        return new BrowserCompletionResponse(responseData);
      } catch (error) {
        if (error.name === 'AbortError') {
          throw new Error(`Request timeout after ${this.config.timeout}ms`);
        }

        if (attempt === maxRetries) {
          throw new Error(`OpenAI API request failed after ${maxRetries} attempts: ${error.message}`);
        }

        // Network errors - retry with exponential backoff
        if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
          const delay = Math.pow(2, attempt) * 1000;
          console.warn(`[OpenAI Service] Network error, retrying in ${delay}ms...`);
          await this._sleep(delay);
          continue;
        }

        // Don't retry for other errors
        throw new Error(`OpenAI API request failed: ${error.message}`);
      }
    }
  }

  _sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

class BrowserCompletionResponse {
  constructor(responseData) {
    this.data = responseData;
    this.choices = responseData.choices || [];
  }

  getContent() {
    if (this.choices.length === 0) {
      throw new Error("No choices available in response");
    }
    return this.choices[0].message?.content || "";
  }

  getUsage() {
    return this.data.usage;
  }

  hasToolCalls() {
    return this.choices.length > 0 && this.choices[0].message?.tool_calls?.length > 0;
  }

  getToolCalls() {
    if (!this.hasToolCalls()) {
      return [];
    }
    return this.choices[0].message.tool_calls;
  }

  getFirstToolCall() {
    const toolCalls = this.getToolCalls();
    return toolCalls.length > 0 ? toolCalls[0] : null;
  }

  getFinishReason() {
    return this.choices[0]?.finish_reason;
  }

  getMessage() {
    return this.choices[0]?.message;
  }

  parseJSONContent() {
    const content = this.getContent();
    if (!content) {
      throw new Error("No content available for JSON parsing");
    }
    
    try {
      // Clean the content by removing markdown code blocks if present
      let cleanContent = content.trim();
      
      // Remove markdown JSON code blocks
      if (cleanContent.startsWith('```json') && cleanContent.endsWith('```')) {
        cleanContent = cleanContent.slice(7, -3).trim();
      } else if (cleanContent.startsWith('```') && cleanContent.endsWith('```')) {
        cleanContent = cleanContent.slice(3, -3).trim();
      }
      
      return JSON.parse(cleanContent);
    } catch (error) {
      // If parsing fails, try to extract JSON from the response
      try {
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        }
      } catch (secondError) {
        // If all else fails, throw the original error with context
        throw new Error(`Failed to parse JSON content: ${error.message}. Content: ${content.substring(0, 200)}...`);
      }
      
      throw new Error(`Failed to parse JSON content: ${error.message}. Content: ${content.substring(0, 200)}...`);
    }
  }
}

// Export for both CommonJS (tests) and global scope (extension)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    BrowserOpenAIService
  };
} else {
  // Export for browser/extension environment
  if (typeof window !== 'undefined') {
    window.BrowserOpenAIService = BrowserOpenAIService;
  } else if (typeof globalThis !== 'undefined') {
    globalThis.BrowserOpenAIService = BrowserOpenAIService;
  }
}
