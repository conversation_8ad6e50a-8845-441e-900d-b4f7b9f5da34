const fs = require('fs');
const {
  Message,
  CompletionResponse,
  CompletionOptions,
  ServiceConfig,
  Choice,
  Usage,
  PromptTokensDetails,
  CompletionTokensDetails,
  ValidationError,
  APIError,
  NetworkError
} = require('../types.js');

class OpenAIService {
  constructor(config = {}) {
    this.config = new ServiceConfig(config);
    this.loadEnvironmentVariables();
    this.validateConfig();
  }

  loadEnvironmentVariables() {
    try {
      if (fs.existsSync('.env')) {
        const envContent = fs.readFileSync('.env', 'utf8');
        const envVars = {};
        
        envContent.split('\n').forEach(line => {
          const [key, value] = line.split('=');
          if (key && value) {
            envVars[key.trim()] = value.trim();
          }
        });

        // Use environment API key if not provided in config
        if (!this.config.apiKey && envVars.OPENAI_API_KEY) {
          this.config.apiKey = envVars.OPENAI_API_KEY;
        }
      }
    } catch (error) {
      console.warn('[OpenAI Service] Could not load .env file:', error.message);
    }
  }

  validateConfig() {
    if (!this.config.apiKey) {
      throw new ValidationError('API key is required. Provide via config or OPENAI_API_KEY environment variable.');
    }

    if (!this.config.baseUrl || !this.config.baseUrl.startsWith('http')) {
      throw new ValidationError('Valid baseUrl is required.');
    }
  }

  validateMessages(messages) {
    if (!Array.isArray(messages) || messages.length === 0) {
      throw new ValidationError('Messages must be a non-empty array.');
    }

    messages.forEach((msg, index) => {
      if (!msg.role || !['system', 'user', 'assistant'].includes(msg.role)) {
        throw new ValidationError(`Message ${index}: Invalid role. Must be 'system', 'user', or 'assistant'.`);
      }
      if (typeof msg.content !== 'string' || msg.content.trim() === '') {
        throw new ValidationError(`Message ${index}: Content must be a non-empty string.`);
      }
    });
  }

  buildRequestBody(messages, options = {}) {
    const completionOptions = new CompletionOptions(options);
    
    const requestBody = {
      model: completionOptions.model,
      messages: messages.map(msg => ({
        role: msg.role,
        content: msg.content
      })),
      max_tokens: completionOptions.max_tokens,
      temperature: completionOptions.temperature,
      top_p: completionOptions.top_p,
      frequency_penalty: completionOptions.frequency_penalty,
      presence_penalty: completionOptions.presence_penalty,
      ...(completionOptions.stop && { stop: completionOptions.stop }),
      ...(completionOptions.n !== 1 && { n: completionOptions.n }),
      ...(completionOptions.stream && { stream: completionOptions.stream }),
      ...(completionOptions.logprobs && { logprobs: completionOptions.logprobs }),
      ...(completionOptions.top_logprobs && { top_logprobs: completionOptions.top_logprobs }),
      ...(completionOptions.service_tier && { service_tier: completionOptions.service_tier }),
      ...(completionOptions.seed && { seed: completionOptions.seed })
    };

    // Add tool calling support
    if (options.tools) {
      requestBody.tools = options.tools;
      if (options.tool_choice) {
        requestBody.tool_choice = options.tool_choice;
      }
    }

    // Add structured output support
    if (options.response_format) {
      requestBody.response_format = options.response_format;
    }

    return requestBody;
  }

  async makeRequest(requestBody) {
    const url = `${this.config.baseUrl}/chat/completions`;
    
    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`;
        throw new APIError(errorMessage, response.status);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof APIError) {
        throw error;
      }
      throw new NetworkError(`Request failed: ${error.message}`);
    }
  }

  parseResponse(responseData) {
    try {
      // Parse choices
      const choices = responseData.choices?.map(choice => {
        const message = new Message(
          choice.message.role,
          choice.message.content
        );
        
        // Add tool calls if present
        if (choice.message.tool_calls) {
          message.tool_calls = choice.message.tool_calls;
        }
        
        return new Choice(
          choice.index,
          message,
          choice.finish_reason,
          choice.logprobs
        );
      }) || [];

      // Parse usage
      const usageData = responseData.usage;
      const usage = usageData ? new Usage(
        usageData.prompt_tokens,
        usageData.completion_tokens,
        usageData.total_tokens,
        usageData.prompt_tokens_details ? new PromptTokensDetails(
          usageData.prompt_tokens_details.audio_tokens,
          usageData.prompt_tokens_details.cached_tokens
        ) : null,
        usageData.completion_tokens_details ? new CompletionTokensDetails(
          usageData.completion_tokens_details.reasoning_tokens,
          usageData.completion_tokens_details.audio_tokens,
          usageData.completion_tokens_details.accepted_prediction_tokens,
          usageData.completion_tokens_details.rejected_prediction_tokens
        ) : null
      ) : null;

      return new CompletionResponse(
        responseData.id,
        responseData.object,
        responseData.created,
        responseData.model,
        choices,
        usage,
        responseData.service_tier,
        responseData.system_fingerprint
      );
    } catch (error) {
      throw new ValidationError(`Failed to parse response: ${error.message}`);
    }
  }

  // Main chat completion method
  async chat(messages, options = {}) {
    this.validateMessages(messages);
    
    const requestBody = this.buildRequestBody(messages, options);
    const responseData = await this.makeRequest(requestBody);
    
    return this.parseResponse(responseData);
  }

  // Convenience method for simple prompts
  async simple(prompt, systemPrompt = null, options = {}) {
    const messages = [];
    
    if (systemPrompt) {
      messages.push(Message.system(systemPrompt));
    }
    
    messages.push(Message.user(prompt));
    
    return await this.chat(messages, options);
  }

  // Convenience method with system message
  async withSystem(systemPrompt, userPrompt, options = {}) {
    const messages = [
      Message.system(systemPrompt),
      Message.user(userPrompt)
    ];
    
    return await this.chat(messages, options);
  }

  // Tool calling method
  async callWithTools(messages, tools, toolChoice = "auto", options = {}) {
    const toolOptions = {
      ...options,
      tools: tools,
      tool_choice: toolChoice,
    };

    return await this.chat(messages, toolOptions);
  }

  // JSON structured output method
  async getJSONResponse(messages, options = {}) {
    const jsonOptions = {
      ...options,
      response_format: { type: "json_object" },
    };

    return await this.chat(messages, jsonOptions);
  }

  // Convenience method for JSON with system message
  async withSystemJSON(systemPrompt, userPrompt, options = {}) {
    const messages = [
      Message.system(systemPrompt),
      Message.user(userPrompt)
    ];

    return await this.getJSONResponse(messages, options);
  }

  // Utility methods
  createMessage(role, content) {
    return new Message(role, content);
  }

  createSystemMessage(content) {
    return Message.system(content);
  }

  createUserMessage(content) {
    return Message.user(content);
  }

  createAssistantMessage(content) {
    return Message.assistant(content);
  }

  // Token cost calculation (approximate pricing)
  calculateCost(usage, model = null) {
    if (!usage) return 0;

    // Approximate pricing per 1K tokens (as of 2025)
    const pricing = {
      'gpt-4.1-nano': { input: 0.00015, output: 0.0006 },
      'gpt-4o-mini': { input: 0.00015, output: 0.0006 },
      'gpt-4o': { input: 0.005, output: 0.015 },
      'gpt-4': { input: 0.03, output: 0.06 }
    };

    const modelKey = model || this.config.defaultModel;
    const rates = pricing[modelKey] || pricing['gpt-4o-mini'];

    const inputCost = (usage.prompt_tokens / 1000) * rates.input;
    const outputCost = (usage.completion_tokens / 1000) * rates.output;

    return {
      inputCost,
      outputCost,
      totalCost: inputCost + outputCost,
      currency: 'USD'
    };
  }

  // Format usage for human reading
  formatUsage(usage) {
    if (!usage) return 'No usage data available';

    return {
      tokens: {
        prompt: usage.prompt_tokens,
        completion: usage.completion_tokens,
        total: usage.total_tokens
      },
      details: {
        cached: usage.prompt_tokens_details?.cached_tokens || 0,
        reasoning: usage.completion_tokens_details?.reasoning_tokens || 0
      },
      cost: this.calculateCost(usage)
    };
  }

  // Validate response structure
  validateResponse(response) {
    if (!response || typeof response !== 'object') {
      throw new ValidationError('Invalid response: not an object');
    }

    if (!response.choices || !Array.isArray(response.choices) || response.choices.length === 0) {
      throw new ValidationError('Invalid response: no choices available');
    }

    if (!response.choices[0].message?.content) {
      throw new ValidationError('Invalid response: no content in first choice');
    }

    return true;
  }
}

// Export for Node.js
if (typeof module !== 'undefined' && module.exports) {
  module.exports = OpenAIService;
}